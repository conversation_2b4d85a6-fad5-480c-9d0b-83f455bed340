#!/usr/bin/env node

/**
 * Скрипт для генерации TypeScript типов из схемы PocketBase
 * Использование: bun generate-types.js
 */

import fs from 'fs';
import path from 'path';

// Читаем схему PocketBase с fallback логикой
let schemaPath = path.join(process.cwd(), '..', 'pb_schema.json');

// Проверяем, существует ли файл в корне проекта
if (!fs.existsSync(schemaPath)) {
  // Если нет, ищем в public (режим разработки)
  schemaPath = path.join(process.cwd(), 'public', 'pb_schema.json');
}

if (!fs.existsSync(schemaPath)) {
  // Если нет, ищем в dist/client (продакшен)
  schemaPath = path.join(process.cwd(), 'dist', 'client', 'pb_schema.json');
}

if (!fs.existsSync(schemaPath)) {
  console.error('❌ Файл pb_schema.json не найден ни в одном из ожидаемых мест:');
  console.error('  - ../pb_schema.json');
  console.error('  - public/pb_schema.json');
  console.error('  - dist/client/pb_schema.json');
  process.exit(1);
}

console.log(`📖 Читаем схему из: ${schemaPath}`);
const schema = JSON.parse(fs.readFileSync(schemaPath, 'utf8'));

// Функция для преобразования типа поля PocketBase в TypeScript тип
function mapFieldType(field) {
  switch (field.type) {
    case 'text':
    case 'email':
    case 'url':
    case 'editor':
      return 'string';
    case 'number':
      return 'number';
    case 'bool':
      return 'boolean';
    case 'date':
    case 'autodate':
      return 'string';
    case 'file':
      return 'string | string[]';
    case 'relation':
      return field.maxSelect === 1 ? 'string' : 'string[]';
    case 'select':
      if (field.values && field.values.length > 0) {
        return field.values.map(v => `'${v}'`).join(' | ');
      }
      return 'string';
    case 'password':
      return 'string';
    default:
      return 'any';
  }
}

// Функция для генерации интерфейса коллекции
function generateCollectionInterface(collection) {
  if (collection.system || collection.type === 'auth') {
    return null; // Пропускаем системные коллекции
  }

  const interfaceName = collection.name.split('_').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join('');

  let interfaceCode = `// Интерфейс для коллекции ${collection.name}\n`;
  interfaceCode += `export interface ${interfaceName} {\n`;

  // Добавляем поля
  collection.fields.forEach(field => {
    if (field.system) return; // Пропускаем системные поля кроме id, created, updated

    const isOptional = !field.required || field.name === 'created' || field.name === 'updated';
    const fieldType = mapFieldType(field);
    
    interfaceCode += `  ${field.name}${isOptional ? '?' : ''}: ${fieldType};\n`;
  });

  // Добавляем expand поле для связей
  const relationFields = collection.fields.filter(f => f.type === 'relation' && !f.system);
  if (relationFields.length > 0) {
    interfaceCode += `  expand?: {\n`;
    relationFields.forEach(field => {
      const relatedCollection = schema.find(c => c.id === field.collectionId);
      if (relatedCollection) {
        const relatedInterfaceName = relatedCollection.name.split('_').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join('');
        
        const expandType = field.maxSelect === 1 ? relatedInterfaceName : `${relatedInterfaceName}[]`;
        interfaceCode += `    ${field.name}?: ${expandType};\n`;
      }
    });
    interfaceCode += `  };\n`;
  }

  interfaceCode += `}\n\n`;
  return interfaceCode;
}

// Генерируем типы для всех коллекций
let typesCode = `// Автоматически сгенерированные типы из схемы PocketBase\n`;
typesCode += `// Не редактируйте этот файл вручную!\n\n`;

// Базовый интерфейс для записи
typesCode += `export interface BaseRecord {\n`;
typesCode += `  id: string;\n`;
typesCode += `  created: string;\n`;
typesCode += `  updated: string;\n`;
typesCode += `}\n\n`;

// Генерируем интерфейсы для коллекций
const collectionInterfaces = [];
schema.forEach(collection => {
  const interfaceCode = generateCollectionInterface(collection);
  if (interfaceCode) {
    collectionInterfaces.push({
      name: collection.name,
      code: interfaceCode
    });
  }
});

// Добавляем интерфейсы в код
collectionInterfaces.forEach(({ code }) => {
  typesCode += code;
});

// Создаем union тип для всех коллекций
const collectionNames = collectionInterfaces.map(({ name }) => `'${name}'`).join(' | ');
typesCode += `// Union тип для названий коллекций\n`;
typesCode += `export type CollectionName = ${collectionNames};\n\n`;

// Записываем файл
const outputPath = path.join(process.cwd(), 'src', 'lib', 'pocketbase-types.ts');
fs.writeFileSync(outputPath, typesCode);

console.log(`✅ Типы успешно сгенерированы в ${outputPath}`);
console.log(`📊 Обработано коллекций: ${collectionInterfaces.length}`);
collectionInterfaces.forEach(({ name }) => {
  console.log(`   - ${name}`);
});
