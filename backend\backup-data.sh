#!/bin/bash

# Скрипт для резервного копирования данных PocketBase

set -e

BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="pocketbase_backup_${TIMESTAMP}"

echo "💾 Создаем резервную копию данных PocketBase..."

# Создаем директорию для бэкапов
mkdir -p ${BACKUP_DIR}

# Проверяем, запущен ли контейнер
CONTAINER_NAME=${1:-"backend-pocketbase-1"}

if docker ps | grep -q ${CONTAINER_NAME}; then
    echo "📦 Создаем бэкап из запущенного контейнера: ${CONTAINER_NAME}"
    
    # Создаем бэкап из контейнера
    docker exec ${CONTAINER_NAME} tar czf /tmp/${BACKUP_NAME}.tar.gz -C /pb pb_data
    docker cp ${CONTAINER_NAME}:/tmp/${BACKUP_NAME}.tar.gz ${BACKUP_DIR}/
    docker exec ${CONTAINER_NAME} rm /tmp/${BACKUP_NAME}.tar.gz
    
elif [ -d "pb_data" ]; then
    echo "📁 Создаем бэкап из локальной папки pb_data"
    
    # Создаем бэкап из локальной папки
    tar czf ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz pb_data
    
else
    echo "❌ Не найден ни контейнер, ни локальная папка pb_data"
    echo "Использование: $0 [имя_контейнера]"
    exit 1
fi

echo "✅ Бэкап создан: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
echo "📊 Размер бэкапа:"
ls -lh ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz

echo ""
echo "🔄 Для восстановления используйте:"
echo "tar xzf ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
echo ""
echo "📋 Все бэкапы:"
ls -lh ${BACKUP_DIR}/
