[{"name": "<PERSON><PERSON><PERSON>а", "phone": "+7 (999) 123-45-67", "message": "Хочу записаться на консультацию к стоматологу-терапевту", "status": "new", "source": "contact-form-header", "ip_address": "*************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}, {"name": "Петр <PERSON><PERSON><PERSON><PERSON><PERSON>", "phone": "+7 (985) 234-56-78", "message": "Интересует имплантация зубов, хочу узнать цены", "status": "in_progress", "source": "services-page-form", "ip_address": "*************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "processed_at": "2024-01-15T10:30:00Z", "processed_by": "Мен<PERSON><PERSON><PERSON><PERSON>р Смирнова", "notes": "Клиент заинтересован в имплантации 2 зубов, отправлен прайс-лист"}, {"name": "Мария Козлова", "phone": "+7 (916) 345-67-89", "message": "", "status": "completed", "source": "hero-section-form", "ip_address": "*************", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1", "processed_at": "2024-01-14T16:45:00Z", "processed_by": "Администратор", "notes": "Клиент записан на профилактический осмотр 20.01.2024 в 14:00"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phone": "+7 (903) 456-78-90", "message": "Срочно нужна помощь, болит зуб", "status": "completed", "source": "emergency-form", "ip_address": "*************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0", "processed_at": "2024-01-13T09:15:00Z", "processed_by": "Дежурный врач", "notes": "Экстренный прием, проведено лечение пульпита"}, {"name": "Елена Новикова", "phone": "+7 (926) 567-89-01", "message": "Хочу поставить брекеты, когда можно прийти на консультацию?", "status": "new", "source": "orthodontics-page", "ip_address": "*************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36"}, {"name": "Александр Морозов", "phone": "+7 (977) 678-90-12", "message": "Интересует отбеливание зубов", "status": "cancelled", "source": "cosmetic-dentistry-page", "ip_address": "*************", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15", "processed_at": "2024-01-12T11:20:00Z", "processed_by": "Менед<PERSON><PERSON>р Петрова", "notes": "Клиент передумал, отказался от услуги"}, {"name": "Ольга Федорова", "phone": "+7 (965) 789-01-23", "message": "Нужна справка для работы", "status": "completed", "source": "contact-form-footer", "ip_address": "*************", "user_agent": "Mozilla/5.0 (Android 14; Mobile; rv:120.0) Gecko/120.0 Firefox/120.0", "processed_at": "2024-01-11T14:10:00Z", "processed_by": "Регистратор", "notes": "Справка выдана, оплата произведена"}, {"name": "Игорь Лебедев", "phone": "+7 (954) 890-12-34", "message": "Хочу записаться к детскому стоматологу для ребенка 7 лет", "status": "in_progress", "source": "pediatric-dentistry-page", "ip_address": "*************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "processed_at": "2024-01-10T13:25:00Z", "processed_by": "Детский врач", "notes": "Консультация назначена на 18.01.2024 в 16:30"}, {"name": "Татьяна Соколова", "phone": "+7 (943) 901-23-45", "message": "", "status": "new", "source": "promo-page-form", "ip_address": "*************", "user_agent": "Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1"}, {"name": "<PERSON><PERSON>р<PERSON><PERSON><PERSON> Орлов", "phone": "+7 (932) 012-34-56", "message": "Интересует протезирование зубов, какие варианты есть?", "status": "new", "source": "prosthetics-page", "ip_address": "*************", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}]