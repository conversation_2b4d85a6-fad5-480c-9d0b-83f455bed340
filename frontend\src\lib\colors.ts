// Основные цвета бренда
export const COLORS = {
  // Основные зеленые оттенки
  primary: {
    50: '#f0f9eb',
    100: '#d9f0d1',
    200: '#b8e3ab',
    300: '#8fd37f',
    400: '#6bc25d',
    500: '#4E8C29', // Основной зеленый
    600: '#3e7a21',
    700: '#2f6619',
    800: '#215211',
    900: '#143e0a',
  },
  
  // Вторичные цвета
  secondary: {
    50: '#faf6f0',
    100: '#f4ebdc',
    200: '#e8d7b9',
    300: '#d9be91',
    400: '#c9a56a',
    500: '#b88c43',
    600: '#9c7336',
    700: '#805a29',
    800: '#64421c',
    900: '#482a0f',
  },
  
  // Серые оттенки
  gray: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
  },
  
  // Акцентные цвета
  accent: {
    green: '#8BC34A',
    lightGreen: '#C5E1A5',
    gold: '#FFD700',
    white: '#ffffff',
    black: '#000000',
  }
} as const

// Градиенты
export const GRADIENTS = {
  primary: 'linear-gradient(135deg, #4E8C29 0%, #8BC34A 100%)',
  light: 'linear-gradient(135deg, #C5E1A5 0%, #E8F5E8 100%)',
  accent: 'linear-gradient(135deg, #4E8C29 0%, #8BC34A 50%, #C5E1A5 100%)',
  overlay: 'linear-gradient(180deg, rgba(0,0,0,0.3) 0%, transparent 50%, rgba(0,0,0,0.3) 100%)'
} as const

// Тени
export const SHADOWS = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  glow: '0 0 20px rgba(139, 195, 74, 0.3)',
  glowStrong: '0 0 30px rgba(139, 195, 74, 0.5)'
} as const