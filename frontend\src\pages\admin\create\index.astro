---
import Layout from '@/layouts/Layout.astro';
import { isUserAuthenticated } from '@/middleware/auth';

// Проверяем авторизацию
const isAuthenticated = isUserAuthenticated(Astro.locals);
if (!isAuthenticated) {
  return Astro.redirect('/admin');
}

// Получаем список доступных коллекций для создания записей
const { getCreateableCollections, getCollectionDisplayName, getCollectionDescription } = await import('@/lib/pocketbase-schema');

const createableCollections = await getCreateableCollections();

// Дополнительная информация для отображения
const collectionInfo: Record<string, { icon: string; color: string }> = {
  doctors: { icon: '👨‍⚕️', color: 'bg-blue-500' },
  services: { icon: '🏥', color: 'bg-green-500' },
  service_categories: { icon: '📂', color: 'bg-indigo-500' },
  prices: { icon: '💰', color: 'bg-emerald-500' },
  reviews: { icon: '⭐', color: 'bg-purple-500' },
  faq: { icon: '❓', color: 'bg-yellow-500' },
  news: { icon: '📰', color: 'bg-red-500' },
  promos: { icon: '🎯', color: 'bg-orange-500' },
  pages: { icon: '📄', color: 'bg-indigo-500' },
  html_blocks: { icon: '🧩', color: 'bg-cyan-500' },
  personal: { icon: '👥', color: 'bg-teal-500' }
};

const collections = createableCollections.map(collection => ({
  name: collection.name,
  title: getCollectionDisplayName(collection.name),
  description: `Добавить ${getCollectionDescription(collection.name).toLowerCase()}`,
  icon: collectionInfo[collection.name]?.icon || '📄',
  color: collectionInfo[collection.name]?.color || 'bg-gray-500'
}));
---

<Layout title="Создание записей - Админ-панель">
  <div class="min-h-screen bg-gradient-to-br from-[#8BC34A]/10 via-white to-[#8BC34A]/5">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center gap-3">
            <a href="/admin" class="text-[#8BC34A] hover:text-[#4E8C29] transition-colors">
              ← Назад к панели
            </a>
            <span class="text-gray-300">|</span>
            <h1 class="text-xl font-bold text-gray-900">Создание записей</h1>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Выберите тип записи для создания</h2>
        <p class="text-gray-600">Создайте новую запись в одной из доступных коллекций</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {collections.map((collection) => (
          <a 
            href={`/admin/create/${collection.name}`}
            class="block bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
          >
            <div class="p-6">
              <div class="flex items-center gap-4 mb-4">
                <div class={`w-12 h-12 ${collection.color} rounded-lg flex items-center justify-center text-white text-xl`}>
                  {collection.icon}
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">{collection.title}</h3>
                  <p class="text-sm text-gray-600">{collection.description}</p>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-500">Коллекция: {collection.name}</span>
                <svg class="w-5 h-5 text-[#8BC34A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
              </div>
            </div>
          </a>
        ))}
      </div>

      <!-- Quick Actions -->
      <div class="mt-12 bg-white rounded-lg shadow-sm border p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Быстрые действия</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a 
            href="https://pb.stom-line.ru/_/"
            target="_blank"
            class="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div class="w-10 h-10 bg-[#8BC34A] rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <div>
              <h4 class="font-medium text-gray-900">PocketBase Admin</h4>
              <p class="text-sm text-gray-600">Полная админ-панель PocketBase</p>
            </div>
          </a>
          <a 
            href="/admin"
            class="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div class="w-10 h-10 bg-gray-500 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
              </svg>
            </div>
            <div>
              <h4 class="font-medium text-gray-900">Главная панель</h4>
              <p class="text-sm text-gray-600">Вернуться к главной админ-панели</p>
            </div>
          </a>
        </div>
      </div>
    </main>
  </div>
</Layout>
