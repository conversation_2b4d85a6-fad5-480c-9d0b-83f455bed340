# Настройка уведомлений о заявках на обратный звонок

## 📋 Обзор

Хук `callback-notifications.js` автоматически отправляет уведомления при создании новой заявки в коллекции `callback_requests`. Поддерживаются два канала уведомлений:

- 📧 **Email** - через SMTP
- 📱 **Telegram** - через Telegram Bot API

## 🔧 Настройка переменных окружения

Скопируйте `.env.example` в `.env` и настройте следующие параметры:

### Email уведомления

```bash
# SMTP настройки
SMTP_HOST=smtp.gmail.com              # SMTP сервер
SMTP_PORT=587                         # SMTP порт
SMTP_USERNAME=<EMAIL>    # Логин SMTP
SMTP_PASSWORD=your-app-password       # Пароль приложения

# Email адреса
NOTIFICATION_EMAIL_FROM=<EMAIL>  # От кого
NOTIFICATION_EMAIL_TO=<EMAIL>      # Кому
```

### Telegram уведомления

```bash
TELEGRAM_BOT_TOKEN=12********:ABCdefGHIjklMNOpqrsTUVwxyz  # Токен бота
TELEGRAM_CHAT_ID=-10012********                          # ID чата
```

### Общие настройки

```bash
SITE_URL=https://yourdomain.com  # URL сайта для ссылок
```

## 📧 Настройка Email (Gmail)

### 1. Включите двухфакторную аутентификацию
- Перейдите в настройки Google аккаунта
- Включите 2FA

### 2. Создайте пароль приложения
- Перейдите в [Пароли приложений](https://myaccount.google.com/apppasswords)
- Выберите "Почта" и "Другое устройство"
- Скопируйте сгенерированный пароль в `SMTP_PASSWORD`

### 3. Настройте переменные
```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=generated-app-password
```

## 📱 Настройка Telegram

### 1. Создайте бота
- Напишите [@BotFather](https://t.me/BotFather)
- Отправьте `/newbot`
- Следуйте инструкциям
- Скопируйте токен в `TELEGRAM_BOT_TOKEN`

### 2. Получите Chat ID

#### Для личных сообщений:
- Напишите [@userinfobot](https://t.me/userinfobot)
- Отправьте любое сообщение
- Скопируйте ваш ID в `TELEGRAM_CHAT_ID`

#### Для группы:
- Добавьте бота в группу
- Сделайте его администратором
- Отправьте сообщение в группу
- Перейдите по ссылке: `https://api.telegram.org/botYOUR_BOT_TOKEN/getUpdates`
- Найдите `"chat":{"id":-10012********}` в ответе
- Скопируйте ID (с минусом!) в `TELEGRAM_CHAT_ID`

## 🚀 Запуск с уведомлениями

### Локально
```bash
# Настройте .env файл
cp .env.example .env
nano .env

# Запустите с переменными окружения
docker-compose up --build
```

### В продакшене
```bash
# Настройте переменные в docker-compose.prod.yml или через environment
export SMTP_USERNAME=<EMAIL>
export SMTP_PASSWORD=your-app-password
export TELEGRAM_BOT_TOKEN=your-bot-token
export TELEGRAM_CHAT_ID=your-chat-id

docker-compose -f docker-compose.prod.yml up -d
```

## 📝 Пример уведомления

### Email
```
Тема: 🔔 Новая заявка на обратный звонок - Иван Петров

📞 Новая заявка на обратный звонок

👤 Имя: Иван Петров
📱 Телефон: +7 (999) 123-45-67
💬 Сообщение: Хочу записаться на консультацию
🌐 IP адрес: *************
⏰ Время: 05.07.2024, 15:30
🆔 ID заявки: abc123def456

[📋 Открыть в админке]
```

### Telegram
```
🔔 Новая заявка на обратный звонок

👤 Имя: Иван Петров
📱 Телефон: +7 (999) 123-45-67
💬 Сообщение: Хочу записаться на консультацию
🌐 IP: *************
⏰ Время: 05.07.2024, 15:30
🆔 ID: abc123def456

📋 Открыть в админке
```

## 🔍 Отладка

### Проверка логов
```bash
# Просмотр логов контейнера
docker logs backend-pocketbase-1 -f

# Поиск сообщений хука
docker logs backend-pocketbase-1 2>&1 | grep "заявка"
```

### Тестирование уведомлений
1. Создайте тестовую заявку через API или админку
2. Проверьте логи на наличие сообщений:
   - `✅ Email уведомление отправлено`
   - `✅ Telegram уведомление отправлено`
3. Если есть ошибки, проверьте настройки переменных окружения

## ⚠️ Устранение проблем

### Email не отправляется
- Проверьте правильность SMTP настроек
- Убедитесь, что используете пароль приложения, а не основной пароль
- Проверьте, что 2FA включена в Google аккаунте

### Telegram не работает
- Проверьте правильность токена бота
- Убедитесь, что Chat ID указан с правильным знаком (- для групп)
- Проверьте, что бот добавлен в группу и имеет права администратора

### Хук не срабатывает
- Убедитесь, что файл `callback-notifications.js` находится в папке `pb_hooks`
- Проверьте логи PocketBase на наличие ошибок загрузки хука
- Убедитесь, что коллекция называется именно `callback_requests`

## 🔒 Безопасность

- Никогда не коммитьте `.env` файл с реальными данными
- Используйте пароли приложений, а не основные пароли
- Регулярно обновляйте токены и пароли
- Ограничьте права Telegram бота только необходимыми
