/**
 * PocketBase хук для отправки уведомлений о новых заявках на обратный звонок
 * 
 * Отправляет уведомления на email и в Telegram при создании новой записи
 * в коллекции callback_requests
 * 
 * Переменные окружения:
 * - SMTP_HOST - SMTP сервер (например: smtp.gmail.com)
 * - SMTP_PORT - SMTP порт (например: 587)
 * - SMTP_USERNAME - логин SMTP
 * - SMTP_PASSWORD - пароль SMTP
 * - NOTIFICATION_EMAIL_FROM - email отправителя
 * - NOTIFICATION_EMAIL_TO - email получателя уведомлений
 * - TELEGRAM_BOT_TOKEN - токен Telegram бота
 * - TELEGRAM_CHAT_ID - ID чата для уведомлений
 * - SITE_URL - URL сайта для ссылок
 */

// Настройки уведомлений
const NOTIFICATION_CONFIG = {
  // Email настройки
  smtp: {
    host: $os.getenv('SMTP_HOST') || 'smtp.gmail.com',
    port: parseInt($os.getenv('SMTP_PORT')) || 587,
    username: $os.getenv('SMTP_USERNAME') || '',
    password: $os.getenv('SMTP_PASSWORD') || '',
    tls: true
  },
  
  // Email адреса
  email: {
    from: $os.getenv('NOTIFICATION_EMAIL_FROM') || '<EMAIL>',
    to: $os.getenv('NOTIFICATION_EMAIL_TO') || '<EMAIL>'
  },
  
  // Telegram настройки
  telegram: {
    botToken: $os.getenv('TELEGRAM_BOT_TOKEN') || '',
    chatId: $os.getenv('TELEGRAM_CHAT_ID') || ''
  },
  
  // Общие настройки
  siteUrl: $os.getenv('SITE_URL') || 'https://example.com'
};

/**
 * Форматирует дату в читаемый вид
 */
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleString('ru-RU', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'Europe/Moscow'
  });
}

/**
 * Отправляет email уведомление
 */
function sendEmailNotification(record) {
  if (!NOTIFICATION_CONFIG.smtp.username || !NOTIFICATION_CONFIG.smtp.password) {
    console.log('⚠️ Email уведомления отключены: не настроены SMTP параметры');
    return;
  }

  const subject = `🔔 Новая заявка на обратный звонок - ${record.name || 'Без имени'}`;
  
  const htmlBody = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2c5aa0; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
        📞 Новая заявка на обратный звонок
      </h2>
      
      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #333;">Информация о клиенте:</h3>
        
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; font-weight: bold; width: 120px;">👤 Имя:</td>
            <td style="padding: 8px 0;">${record.name || 'Не указано'}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold;">📱 Телефон:</td>
            <td style="padding: 8px 0;"><a href="tel:${record.phone}" style="color: #2c5aa0; text-decoration: none;">${record.phone || 'Не указан'}</a></td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold;">💬 Сообщение:</td>
            <td style="padding: 8px 0;">${record.message || 'Без сообщения'}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold;">🌐 IP адрес:</td>
            <td style="padding: 8px 0;">${record.ip_address || 'Не определен'}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold;">⏰ Время:</td>
            <td style="padding: 8px 0;">${formatDate(record.created)}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold;">🆔 ID заявки:</td>
            <td style="padding: 8px 0;"><code>${record.id}</code></td>
          </tr>
        </table>
      </div>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${NOTIFICATION_CONFIG.siteUrl}/admin/#/collections/callback_requests/records/${record.id}" 
           style="background: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
          📋 Открыть в админке
        </a>
      </div>
      
      <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-top: 30px; font-size: 12px; color: #666;">
        <p>Это автоматическое уведомление от системы ${NOTIFICATION_CONFIG.siteUrl}</p>
        <p>Для отключения уведомлений обратитесь к администратору.</p>
      </div>
    </div>
  `;

  try {
    $mails.send({
      from: {
        address: NOTIFICATION_CONFIG.email.from,
        name: "Система уведомлений"
      },
      to: [{
        address: NOTIFICATION_CONFIG.email.to
      }],
      subject: subject,
      html: htmlBody
    });
    
    console.log(`✅ Email уведомление отправлено на ${NOTIFICATION_CONFIG.email.to}`);
  } catch (error) {
    console.error('❌ Ошибка отправки email:', error);
  }
}

/**
 * Отправляет уведомление в Telegram
 */
function sendTelegramNotification(record) {
  if (!NOTIFICATION_CONFIG.telegram.botToken || !NOTIFICATION_CONFIG.telegram.chatId) {
    console.log('⚠️ Telegram уведомления отключены: не настроены параметры бота');
    return;
  }

  const message = `
🔔 *Новая заявка на обратный звонок*

👤 *Имя:* ${record.name || 'Не указано'}
📱 *Телефон:* \`${record.phone || 'Не указан'}\`
💬 *Сообщение:* ${record.message || 'Без сообщения'}
🌐 *IP:* \`${record.ip_address || 'Не определен'}\`
⏰ *Время:* ${formatDate(record.created)}
🆔 *ID:* \`${record.id}\`

[📋 Открыть в админке](${NOTIFICATION_CONFIG.siteUrl}/admin/#/collections/callback_requests/records/${record.id})
  `.trim();

  const telegramUrl = `https://api.telegram.org/bot${NOTIFICATION_CONFIG.telegram.botToken}/sendMessage`;
  
  try {
    const response = $http.send({
      url: telegramUrl,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        chat_id: NOTIFICATION_CONFIG.telegram.chatId,
        text: message,
        parse_mode: 'Markdown',
        disable_web_page_preview: true
      })
    });

    if (response.statusCode === 200) {
      console.log('✅ Telegram уведомление отправлено');
    } else {
      console.error('❌ Ошибка Telegram API:', response.statusCode, response.raw);
    }
  } catch (error) {
    console.error('❌ Ошибка отправки в Telegram:', error);
  }
}

/**
 * Основной обработчик события создания записи
 */
onRecordAfterCreateRequest((e) => {
  // Проверяем, что это коллекция callback_requests
  if (e.record.collection().name !== 'callback_requests') {
    return;
  }

  const record = e.record;
  
  console.log(`📞 Новая заявка на обратный звонок: ${record.get('name')} - ${record.get('phone')}`);
  
  // Отправляем уведомления
  try {
    sendEmailNotification({
      id: record.get('id'),
      name: record.get('name'),
      phone: record.get('phone'),
      message: record.get('message'),
      ip_address: record.get('ip_address'),
      created: record.get('created')
    });
  } catch (error) {
    console.error('❌ Ошибка отправки email уведомления:', error);
  }

  try {
    sendTelegramNotification({
      id: record.get('id'),
      name: record.get('name'),
      phone: record.get('phone'),
      message: record.get('message'),
      ip_address: record.get('ip_address'),
      created: record.get('created')
    });
  } catch (error) {
    console.error('❌ Ошибка отправки Telegram уведомления:', error);
  }
}, "callback_requests");

console.log('🔔 Хук уведомлений о заявках на обратный звонок загружен');
