<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест рендеринга HTML в блоках контента</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-block {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .html-content {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #4E8C29;
        }
    </style>
</head>
<body>
    <h1>Тест рендеринга HTML в блоках контента</h1>
    
    <div class="test-block">
        <h2>Обычный текст</h2>
        <p>Это обычный текст без HTML разметки.</p>
    </div>
    
    <div class="test-block">
        <h2>HTML контент</h2>
        <div class="html-content">
            <p>Это <strong>жирный текст</strong> и <em>курсивный текст</em>.</p>
            <ul>
                <li>Первый пункт списка</li>
                <li>Второй пункт списка</li>
                <li>Третий пункт с <a href="#">ссылкой</a></li>
            </ul>
            <p>Абзац с <span style="color: #4E8C29;">цветным текстом</span>.</p>
        </div>
    </div>
    
    <div class="test-block">
        <h2>Примеры HTML для тестирования</h2>
        <p>Вот примеры HTML контента, которые можно использовать в блоках:</p>
        
        <h3>Простая разметка:</h3>
        <code>&lt;p&gt;Обычный текст с &lt;strong&gt;жирным&lt;/strong&gt; и &lt;em&gt;курсивным&lt;/em&gt; текстом.&lt;/p&gt;</code>
        
        <h3>Список:</h3>
        <code>&lt;ul&gt;&lt;li&gt;Пункт 1&lt;/li&gt;&lt;li&gt;Пункт 2&lt;/li&gt;&lt;/ul&gt;</code>
        
        <h3>Ссылка:</h3>
        <code>&lt;p&gt;Текст с &lt;a href="#"&gt;ссылкой&lt;/a&gt;.&lt;/p&gt;</code>
        
        <h3>Цветной текст:</h3>
        <code>&lt;p&gt;Текст с &lt;span style="color: #4E8C29;"&gt;цветным&lt;/span&gt; словом.&lt;/p&gt;</code>
    </div>
    
    <div class="test-block">
        <h2>Инструкции по использованию</h2>
        <ol>
            <li>В админ-панели PocketBase создайте HTML блок с типом 'html'</li>
            <li>В поле content добавьте HTML разметку</li>
            <li>Функция renderBlockContent автоматически определит тип блока и отрендерит HTML</li>
            <li>Для обычных текстовых блоков HTML будет экранирован</li>
        </ol>
    </div>
</body>
</html>
