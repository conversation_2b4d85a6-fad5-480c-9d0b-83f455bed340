# Деплой PocketBase с хуками

## Обзор решения

Теперь **хуки включены в Docker образ**, а **данные хранятся в volume**:
- ✅ **Хуки не перезатираются** при пересборке (включены в образ)
- ✅ **Данные не перезатираются** при пересборке (хранятся в volume)
- ✅ Хуки автоматически переносятся в облако
- ✅ Данные сохраняются между обновлениями
- ✅ Нет необходимости в дополнительных volume для хуков
- ✅ Упрощенный деплой

## Структура

```
backend/
├── hooks/                    # Хуки (включаются в образ)
│   └── sync-meilisearch.js
├── pb_data/                  # Данные (volume)
├── Dockerfile               # Основной Dockerfile
├── docker-compose.yml       # Для разработки
├── docker-compose.prod.yml  # Для продакшена
├── deploy.sh               # Скрипт деплоя
└── .env.example            # Пример переменных
```

## Локальная разработка

1. Скопируйте переменные окружения:
```bash
cp .env.example .env
```

2. Запустите локально:
```bash
docker-compose up --build
```

## Деплой в облако

### Вариант 1: Автоматический деплой

1. Настройте переменные в `.env`:
```bash
DOCKER_REGISTRY=your-registry.com
IMAGE_NAME=pocketbase-app
PB_ENCRYPTION_KEY=your-32-character-key
```

2. Запустите деплой:
```bash
./deploy.sh v1.0.0
```

### Вариант 2: Ручной деплой

1. Соберите образ:
```bash
docker build -t your-registry.com/pocketbase-app:latest .
```

2. Отправьте в реестр:
```bash
docker push your-registry.com/pocketbase-app:latest
```

3. Запустите в облаке:
```bash
docker run -d \
  -p 8090:8090 \
  -v pocketbase_data:/pb/pb_data \
  -e PB_ENCRYPTION_KEY=your-key \
  your-registry.com/pocketbase-app:latest
```

### Вариант 3: Docker Compose в продакшене

```bash
# Настройте переменные окружения
export DOCKER_REGISTRY=your-registry.com
export IMAGE_NAME=pocketbase-app
export TAG=latest
export PB_ENCRYPTION_KEY=your-key

# Запустите
docker-compose -f docker-compose.prod.yml up -d
```

## Проверка хуков

После деплоя проверьте, что хуки на месте:
```bash
docker exec -it container_name ls -la /pb/pb_hooks/
```

## Обновление хуков

1. Измените файлы в папке `hooks/`
2. Пересоберите образ: `./deploy.sh v1.0.1`
3. Обновите контейнер в облаке

## Важные моменты

- 🔒 **Данные**: Хранятся в volume `/pb/pb_data` (**НЕ ПЕРЕЗАТИРАЮТСЯ** при пересборке)
- 🔧 **Хуки**: Включены в образ (**НЕ ПЕРЕЗАТИРАЮТСЯ** при пересборке, обновляются с новой версией образа)
- 📦 **Образ**: Данные исключены из образа через `.dockerignore`
- 🔑 **Безопасность**: Обязательно установите `PB_ENCRYPTION_KEY` в продакшене
- 📊 **Мониторинг**: Включен health check для контроля состояния

## Что происходит при пересборке

1. **Хуки** - остаются в образе, обновляются если изменились
2. **Данные** - остаются в volume, **никогда не затираются**
3. **Конфигурация** - обновляется в образе
4. **База данных** - сохраняется в volume между обновлениями

## Резервное копирование данных

### Создание бэкапа
```bash
# Из запущенного контейнера
./backup-data.sh

# Из конкретного контейнера
./backup-data.sh my-pocketbase-container
```

### Восстановление из бэкапа
```bash
# Восстановить из бэкапа
./restore-data.sh ./backups/pocketbase_backup_20240705_120000.tar.gz

# Восстановить в конкретный контейнер
./restore-data.sh ./backups/backup.tar.gz my-container
```

**⚠️ Важно**: Восстановление перезаписывает текущие данные!
