import PocketBase from 'pocketbase';

const PB_URL = 'https://pb.stom-line.ru';

/**
 * Создает экземпляр PocketBase с авторизацией
 */
export function createAuthenticatedPB(token?: string): PocketBase {
  const pb = new PocketBase(PB_URL);

  const authToken = token || localStorage.getItem('pb_token');
  if (authToken) {
    pb.authStore.save(authToken);
  }

  return pb;
}

/**
 * Авторизация пользователя (админ или обычный пользователь)
 */
export async function authenticateUser(email: string, password: string) {
  const pb = new PocketBase(PB_URL);
  
  try {
    // Сначала пытаемся авторизоваться как суперпользователь (админ) - PocketBase v0.23.0+
    const authData = await pb.collection('_superusers').authWithPassword(email, password);
    return { success: true, data: authData, isAdmin: true };
  } catch (adminError) {
    try {
      // Если не получилось как админ, пытаемся как обычный пользователь
      const authData = await pb.collection('users').authWithPassword(email, password);
      return { success: true, data: authData, isAdmin: false };
    } catch (userError: any) {
      return {
        success: false,
        error: userError?.message || 'Неверный email или пароль'
      };
    }
  }
}

/**
 * Получение статистики по коллекциям
 */
export async function getCollectionsStats(token: string) {
  const pb = createAuthenticatedPB(token);
  
  const collections = ['doctors', 'services', 'promos', 'reviews', 'faq', 'news', 'pages', 'prices', 'html_blocks', 'personal', 'files', 'callback_requests', 'users', 'service_categories'];
  const stats: Record<string, number> = {};
  
  await Promise.all(
    collections.map(async (collection) => {
      try {
        const result = await pb.collection(collection).getList(1, 1);
        stats[collection] = result.totalItems || 0;
      } catch (error) {
        console.error(`Error loading ${collection} stats:`, error);
        stats[collection] = 0;
      }
    })
  );
  
  return stats;
}

/**
 * Получение записей коллекции с пагинацией
 */
export async function getCollectionRecords(
  collection: string,
  page: number = 1,
  perPage: number = 50,
  options?: {
    sort?: string;
    filter?: string;
    expand?: string;
  }
) {
  const pb = createAuthenticatedPB();

  return await pb.collection(collection).getList(page, perPage, {
    sort: options?.sort || '-created',
    filter: options?.filter,
    expand: options?.expand,
  });
}

/**
 * Получение одной записи
 */
export async function getRecord(collection: string, id: string, expand?: string) {
  const pb = createAuthenticatedPB();
  
  return await pb.collection(collection).getOne(id, {
    expand,
  });
}

/**
 * Создание новой записи
 */
export async function createRecord(collection: string, data: any) {
  const pb = createAuthenticatedPB();
  
  return await pb.collection(collection).create(data);
}

/**
 * Обновление записи
 */
export async function updateRecord(collection: string, id: string, data: any) {
  const pb = createAuthenticatedPB();
  
  return await pb.collection(collection).update(id, data);
}

/**
 * Удаление записи
 */
export async function deleteRecord(collection: string, id: string) {
  const pb = createAuthenticatedPB();
  
  return await pb.collection(collection).delete(id);
}

/**
 * Проверка авторизации
 */
export function isAuthenticated(): boolean {
  const token = localStorage.getItem('pb_token');
  return !!token;
}

/**
 * Выход из системы
 */
export function logout() {
  localStorage.removeItem('pb_token');
  document.cookie = 'pb_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
}

/**
 * Сохранение токена авторизации
 */
export function saveAuthToken(token: string) {
  localStorage.setItem('pb_token', token);
  document.cookie = `pb_token=${token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
}

/**
 * Получение URL для файла
 */
export function getFileUrl(record: any, filename: string, collection?: string): string {
  if (!filename || !record) return '';
  
  const collectionName = collection || record.collectionName || record.collectionId;
  return `${PB_URL}/api/files/${collectionName}/${record.id}/${filename}`;
}

/**
 * Типы для коллекций
 */
export interface CollectionRecord {
  id: string;
  created: string;
  updated: string;
  [key: string]: any;
}

export interface ListResult<T = CollectionRecord> {
  page: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
  items: T[];
}

export interface AuthData {
  token: string;
  record: any;
}

export { PB_URL };
