'use client'

import React from 'react'
import { Info, Volume2, Keyboard, Eye, Contrast, Type } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export function AccessibilityInfo() {
  return (
    <Card className="w-full max-w-4xl mx-auto my-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-2xl">
          <Info className="h-6 w-6" />
          Информация о версии для слабовидящих
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid md:grid-cols-2 gap-6">
          {/* Соответствие стандартам */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Соответствие стандартам</h3>
            <div className="space-y-2">
              <Badge variant="secondary" className="mr-2">
                ГОСТ Р 52872-2019
              </Badge>
              <Badge variant="secondary" className="mr-2">
                WCAG 2.1 AA
              </Badge>
              <Badge variant="secondary">
                Приказ Минкомсвязи №467
              </Badge>
            </div>
            <p className="text-sm text-gray-600">
              Версия сайта для слабовидящих полностью соответствует российским требованиям 
              доступности для государственных и медицинских сайтов.
            </p>
          </div>

          {/* Возможности */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Возможности</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Type className="h-4 w-4" />
                <span>Увеличенный шрифт (18-24px)</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Contrast className="h-4 w-4" />
                <span>Высокий контраст</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Keyboard className="h-4 w-4" />
                <span>Навигация с клавиатуры</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Volume2 className="h-4 w-4" />
                <span>Поддержка скринридеров</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Eye className="h-4 w-4" />
                <span>Упрощенный интерфейс</span>
              </div>
            </div>
          </div>
        </div>

        {/* Горячие клавиши */}
        <div className="border-t pt-4">
          <h3 className="text-lg font-semibold mb-3">Горячие клавиши</h3>
          <div className="grid sm:grid-cols-2 gap-3 text-sm">
            <div className="flex justify-between">
              <span>Переключить версию для слабовидящих:</span>
              <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Alt + A</kbd>
            </div>
            <div className="flex justify-between">
              <span>Перейти к основному содержанию:</span>
              <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Tab</kbd>
            </div>
            <div className="flex justify-between">
              <span>Открыть поиск:</span>
              <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl + K</kbd>
            </div>
            <div className="flex justify-between">
              <span>Навигация по ссылкам:</span>
              <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Tab / Shift + Tab</kbd>
            </div>
          </div>
        </div>

        {/* Контактная информация */}
        <div className="border-t pt-4 bg-gray-50 p-4 rounded">
          <h3 className="text-lg font-semibold mb-2">Обратная связь по доступности</h3>
          <p className="text-sm text-gray-600 mb-2">
            Если у вас возникли проблемы с доступностью сайта, пожалуйста, свяжитесь с нами:
          </p>
          <div className="text-sm space-y-1">
            <div>📧 Email: <EMAIL></div>
            <div>📞 Телефон: +7 (XXX) XXX-XX-XX</div>
            <div>🕒 Время работы: Пн-Пт 9:00-18:00</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
