---
import Layout from '@/layouts/Layout.astro';
import { AdminCreatePage } from '@/components/admin/AdminCreatePage';
import { isUserAuthenticated } from '@/middleware/auth';

// Проверяем авторизацию
const isAuthenticated = isUserAuthenticated(Astro.locals);
if (!isAuthenticated) {
  return Astro.redirect('/admin');
}

const { collection } = Astro.params;
const PB_URL = 'https://pb.stom-line.ru';

// Проверяем наличие параметра коллекции
if (!collection) {
  return new Response('Missing collection parameter', { status: 400 });
}

// Проверяем разрешение на создание записей в коллекции
const { isCollectionCreateAllowed, getCollectionSchema } = await import('@/lib/pocketbase-schema');

const isAllowed = await isCollectionCreateAllowed(collection as string);
if (!isAllowed) {
  return new Response('Collection not allowed for creation', { status: 403 });
}

// Получаем схему коллекции для валидации
let collectionSchema = null;
let error = null;

try {
  collectionSchema = await getCollectionSchema(collection as string);

  if (!collectionSchema) {
    error = `Коллекция ${collection} не найдена в схеме`;
  }
} catch (e) {
  error = `Ошибка загрузки схемы: ${e}`;
}
---

<Layout title={`Создание записи: ${collection}`}>
  <div class="min-h-screen bg-gray-50">
    <div class="container mx-auto py-8">
      <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-900">
              Создание новой записи
            </h1>
            <p class="text-gray-600 mt-2">
              Коллекция: <span class="font-semibold">{collection}</span>
            </p>
          </div>
          
          {error && (
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Ошибка</h3>
                  <div class="mt-2 text-sm text-red-700">{error}</div>
                </div>
              </div>
            </div>
          )}
          
          {!error && collectionSchema && (
            <AdminCreatePage
              collection={collection}
              collectionSchema={collectionSchema}
              pbUrl={PB_URL}
              client:only="react"
            />
          )}
          
          {!error && !collectionSchema && (
            <div class="flex items-center justify-center py-12">
              <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Загрузка схемы...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  </div>
</Layout>
