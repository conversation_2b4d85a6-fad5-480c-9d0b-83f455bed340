import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Мокаем window.open
Object.defineProperty(window, 'open', {
  value: vi.fn(),
});

// Мокаем window.confirm
Object.defineProperty(window, 'confirm', {
  value: vi.fn(() => true),
});

// Мокаем window.alert
Object.defineProperty(window, 'alert', {
  value: vi.fn(),
});

// Мокаем console методы для чистых тестов
global.console = {
  ...console,
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn(),
};

// Мокаем fetch
global.fetch = vi.fn();

// Мокаем ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Мокаем IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));
