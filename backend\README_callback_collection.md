# Коллекция "Запросы на обратный звонок" для PocketBase

## 📋 Описание

Этот набор файлов содержит все необходимое для создания и настройки коллекции `callback_requests` в PocketBase для обработки заявок на обратный звонок от клиентов стоматологической клиники.

## 📁 Состав файлов

- `callback_requests_collection.json` - Схема коллекции для импорта в PocketBase
- `callback_requests_sample_data.json` - Тестовые данные для заполнения коллекции
- `import-callback-collection.js` - Скрипт автоматического импорта
- `callback-api-integration-example.ts` - Пример интеграции с API
- `callback_requests_import_guide.md` - Подробное руководство по импорту

## 🚀 Быстрый старт

### 1. Импорт коллекции

#### Автоматический импорт (рекомендуется)

```bash
# Установите зависимости (если нужно)
npm install

# Настройте переменные окружения
export POCKETBASE_URL="http://localhost:8090"
export ADMIN_EMAIL="<EMAIL>"
export ADMIN_PASSWORD="your_admin_password"

# Запустите скрипт импорта
node import-callback-collection.js
```

#### Ручной импорт

1. Откройте админ-панель PocketBase
2. Перейдите в раздел "Collections"
3. Нажмите "Import collections"
4. Загрузите файл `callback_requests_collection.json`

### 2. Обновление фронтенда

Скопируйте код из `callback-api-integration-example.ts` в файл `frontend/src/pages/api/callback.ts`

### 3. Проверка работы

1. Откройте сайт
2. Заполните форму обратного звонка
3. Проверьте, что заявка появилась в админ-панели PocketBase

## 🏗️ Структура коллекции

### Основные поля

| Поле | Тип | Обязательное | Описание |
|------|-----|--------------|----------|
| `name` | text | ✅ | Имя клиента (2-100 символов) |
| `phone` | text | ✅ | Номер телефона с валидацией |
| `message` | text | ❌ | Дополнительное сообщение (до 1000 символов) |

### Поля управления

| Поле | Тип | Значения | Описание |
|------|-----|----------|----------|
| `status` | select | new, in_progress, completed, cancelled | Статус обработки заявки |

### Технические поля

| Поле | Тип | Описание |
|------|-----|----------|
| `source` | text | Источник заявки (страница, форма) |
| `ip_address` | text | IP-адрес клиента |
| `user_agent` | text | User-Agent браузера |
| `processed_at` | date | Дата обработки |
| `processed_by` | text | Кто обработал заявку |
| `notes` | text | Заметки по заявке |

## 🔐 Права доступа

- **Создание**: Публичный доступ (для форм на сайте)
- **Просмотр/Редактирование**: Только авторизованные пользователи
- **Удаление**: Только авторизованные пользователи

## 📊 Примеры использования

### Получение новых заявок

```javascript
// Все новые заявки
const newRequests = await pb.collection('callback_requests').getList(1, 50, {
  filter: 'status = "new"',
  sort: '-created'
});

// Заявки за сегодня
const today = new Date().toISOString().split('T')[0];
const todayRequests = await pb.collection('callback_requests').getList(1, 50, {
  filter: `created >= "${today} 00:00:00"`,
  sort: '-created'
});
```

### Обновление статуса заявки

```javascript
await pb.collection('callback_requests').update(recordId, {
  status: 'completed',
  processed_at: new Date().toISOString(),
  processed_by: 'Менеджер Иванов',
  notes: 'Клиент записан на прием'
});
```

### Создание новой заявки

```javascript
const record = await pb.collection('callback_requests').create({
  name: 'Иван Петров',
  phone: '+7 (999) 123-45-67',
  message: 'Хочу записаться к стоматологу',
  status: 'new',
  source: 'contact-form-header'
});
```

## 🔧 Настройка уведомлений

В файле `callback-api-integration-example.ts` есть заготовка для различных типов уведомлений:

- Email уведомления администратору
- SMS уведомления
- Webhook в CRM систему
- Уведомления в Telegram/Slack

Раскомментируйте и настройте нужные методы в функции `sendNotification`.

## 📈 Мониторинг и аналитика

### Статистика по заявкам

```javascript
// Количество заявок по статусам
const stats = await pb.collection('callback_requests').getList(1, 1, {
  filter: 'status = "new"'
});
console.log('Новых заявок:', stats.totalItems);

// Заявки по источникам
const sourceStats = await pb.collection('callback_requests').getFullList({
  fields: 'source',
  sort: 'source'
});
```

### Экспорт данных

```javascript
// Экспорт всех заявок за месяц
const monthAgo = new Date();
monthAgo.setMonth(monthAgo.getMonth() - 1);

const monthlyRequests = await pb.collection('callback_requests').getFullList({
  filter: `created >= "${monthAgo.toISOString()}"`,
  sort: '-created'
});
```

## 🛡️ Безопасность

1. **Rate Limiting**: Ограничение количества запросов с одного IP
2. **Валидация**: Строгая проверка входящих данных
3. **Санитизация**: Очистка пользовательского ввода
4. **Логирование**: Запись всех операций для аудита

## 🔄 Обновления и миграции

При необходимости изменения структуры коллекции:

1. Создайте резервную копию данных
2. Обновите схему в `callback_requests_collection.json`
3. Выполните миграцию через админ-панель
4. Обновите типы в `frontend/src/lib/pocketbase-types.ts`

## 🐛 Устранение неполадок

### Ошибка "Collection not found"
- Убедитесь, что коллекция импортирована
- Проверьте правильность имени коллекции

### Ошибка "Validation failed"
- Проверьте соответствие данных схеме
- Убедитесь в корректности обязательных полей

### Ошибка "Unauthorized"
- Проверьте настройки прав доступа
- Убедитесь в корректности токена авторизации

## 📞 Поддержка

При возникновении вопросов или проблем:

1. Проверьте логи PocketBase
2. Убедитесь в корректности конфигурации
3. Проверьте версию PocketBase (рекомендуется 0.20+)

## 📝 Лицензия

Этот код предоставляется для использования в проекте стоматологической клиники.
