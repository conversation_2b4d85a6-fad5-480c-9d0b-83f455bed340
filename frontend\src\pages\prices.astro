---
import Layout from '../layouts/Layout.astro';
import { PricesPage } from '../components/prices-page';
import PocketBase from 'pocketbase';
import { getPageSEO } from '../lib/seo-config';
import { isUserAuthenticated } from '../middleware/auth';

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Получаем SEO данные для страницы цен
const seo = getPageSEO('prices');

// URL API
const PUBLIC_API_URL = 'https://pb.stom-line.ru';

// Получаем данные из PocketBase
const pb = new PocketBase(PUBLIC_API_URL);
let priceCategories = [];
let prices = [];

try {
  priceCategories = await pb.collection('service_categories').getFullList({
    sort: 'name',
  });

  prices = await pb.collection('prices').getFullList({
    sort: 'sort_order',
    expand: 'category,service',
  });

  console.log('Получено категорий цен:', priceCategories.length);
  console.log('Получено цен:', prices.length);
} catch (error) {
  console.error('Ошибка при получении данных:', error);
}
---

<Layout
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}
  type={seo.type}
>
  <PricesPage prices={prices} categories={priceCategories} isAuthenticated={isAuthenticated} client:load />
</Layout>
