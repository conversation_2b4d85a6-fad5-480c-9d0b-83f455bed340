/**
 * Тесты для административной панели
 * Проверяют авторизацию, CRUD операции и обработку ошибок
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { CollectionManager } from '@/components/admin/CollectionManager';
import { UniversalRecordEditor } from '@/components/admin/UniversalRecordEditor';
import * as pocketbaseAdmin from '@/lib/pocketbase-admin';

// Мокаем PocketBase функции
vi.mock('@/lib/pocketbase-admin', () => ({
  getCollectionsStats: vi.fn(),
  getCollectionRecords: vi.fn(),
  getRecord: vi.fn(),
  updateRecord: vi.fn(),
  deleteRecord: vi.fn(),
  createRecord: vi.fn(),
  authenticateUser: vi.fn(),
  logout: vi.fn(),
  saveAuthToken: vi.fn(),
}));

// Мокаем localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('AdminDashboard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue('mock-token');
  });

  it('должен отображать форму авторизации для неавторизованного пользователя', () => {
    render(
      <AdminDashboard 
        isAuthenticated={false} 
        pbUrl="https://pb.stom-line.ru" 
      />
    );

    expect(screen.getByText('Панель администратора')).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/email/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/пароль/i)).toBeInTheDocument();
  });

  it('должен отображать панель управления для авторизованного пользователя', async () => {
    const mockStats = {
      doctors: 5,
      services: 10,
      promos: 3,
      reviews: 15,
      faq: 8,
      news: 12,
      pages: 6,
      prices: 20,
    };

    vi.mocked(pocketbaseAdmin.getCollectionsStats).mockResolvedValue(mockStats);

    render(
      <AdminDashboard 
        isAuthenticated={true} 
        pbUrl="https://pb.stom-line.ru" 
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Управление контентом')).toBeInTheDocument();
      expect(screen.getByText('Специалисты')).toBeInTheDocument();
      expect(screen.getByText('Услуги')).toBeInTheDocument();
      expect(screen.getByText('Акции')).toBeInTheDocument();
    });
  });

  it('должен обрабатывать ошибки загрузки статистики', async () => {
    vi.mocked(pocketbaseAdmin.getCollectionsStats).mockRejectedValue(
      new Error('Ошибка сети')
    );

    render(
      <AdminDashboard 
        isAuthenticated={true} 
        pbUrl="https://pb.stom-line.ru" 
      />
    );

    // Проверяем, что ошибка не ломает интерфейс
    await waitFor(() => {
      expect(screen.getByText('Управление контентом')).toBeInTheDocument();
    });
  });
});

describe('CollectionManager', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue('mock-token');
  });

  it('должен загружать и отображать записи коллекции', async () => {
    const mockRecords = {
      items: [
        {
          id: '1',
          name: 'Тестовая запись',
          created: '2024-01-01T00:00:00Z',
          updated: '2024-01-01T00:00:00Z',
        },
      ],
      totalItems: 1,
    };

    vi.mocked(pocketbaseAdmin.getCollectionRecords).mockResolvedValue(mockRecords);

    render(
      <CollectionManager
        collection="promos"
        title="Акции"
        description="Управление акциями"
        pbUrl="https://pb.stom-line.ru"
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Тестовая запись')).toBeInTheDocument();
      expect(screen.getByText('1 записей')).toBeInTheDocument();
    });
  });

  it('должен обрабатывать ошибки загрузки записей', async () => {
    vi.mocked(pocketbaseAdmin.getCollectionRecords).mockRejectedValue(
      new Error('Ошибка загрузки данных')
    );

    render(
      <CollectionManager
        collection="promos"
        title="Акции"
        description="Управление акциями"
        pbUrl="https://pb.stom-line.ru"
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Ошибка загрузки данных')).toBeInTheDocument();
      expect(screen.getByText('Попробовать снова')).toBeInTheDocument();
    });
  });

  it('должен фильтровать записи по поисковому запросу', async () => {
    const mockRecords = {
      items: [
        {
          id: '1',
          name: 'Скидка на лечение',
          created: '2024-01-01T00:00:00Z',
        },
        {
          id: '2',
          name: 'Акция на имплантацию',
          created: '2024-01-01T00:00:00Z',
        },
      ],
      totalItems: 2,
    };

    vi.mocked(pocketbaseAdmin.getCollectionRecords).mockResolvedValue(mockRecords);

    render(
      <CollectionManager
        collection="promos"
        title="Акции"
        description="Управление акциями"
        pbUrl="https://pb.stom-line.ru"
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Скидка на лечение')).toBeInTheDocument();
      expect(screen.getByText('Акция на имплантацию')).toBeInTheDocument();
    });

    // Вводим поисковый запрос
    const searchInput = screen.getByPlaceholderText(/поиск по акции/i);
    fireEvent.change(searchInput, { target: { value: 'скидка' } });

    // Проверяем фильтрацию
    expect(screen.getByText('Скидка на лечение')).toBeInTheDocument();
    expect(screen.queryByText('Акция на имплантацию')).not.toBeInTheDocument();
  });

  it('должен удалять записи', async () => {
    const mockRecords = {
      items: [
        {
          id: '1',
          name: 'Тестовая запись',
          created: '2024-01-01T00:00:00Z',
        },
      ],
      totalItems: 1,
    };

    vi.mocked(pocketbaseAdmin.getCollectionRecords).mockResolvedValue(mockRecords);
    vi.mocked(pocketbaseAdmin.deleteRecord).mockResolvedValue(undefined);

    // Мокаем confirm
    window.confirm = vi.fn(() => true);

    render(
      <CollectionManager
        collection="promos"
        title="Акции"
        description="Управление акциями"
        pbUrl="https://pb.stom-line.ru"
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Тестовая запись')).toBeInTheDocument();
    });

    // Нажимаем кнопку удаления
    const deleteButton = screen.getByRole('button', { name: /удалить/i });
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(pocketbaseAdmin.deleteRecord).toHaveBeenCalledWith('promos', '1');
    });
  });
});

describe('UniversalRecordEditor', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue('mock-token');
  });

  it('должен загружать и отображать запись для редактирования', async () => {
    const mockRecord = {
      id: '1',
      name: 'Тестовая запись',
      description: 'Описание записи',
    };

    const mockSchema = [
      {
        id: 'name',
        name: 'name',
        type: 'text',
        required: true,
      },
      {
        id: 'description',
        name: 'description',
        type: 'text',
        required: false,
      },
    ];

    vi.mocked(pocketbaseAdmin.getRecord).mockResolvedValue(mockRecord);
    // Мокаем получение схемы (это может потребовать дополнительной настройки)

    render(
      <UniversalRecordEditor
        collection="promos"
        recordId="1"
        mode="modal"
        isOpen={true}
        onClose={() => {}}
        pbUrl="https://pb.stom-line.ru"
        token="mock-token"
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Редактирование записи')).toBeInTheDocument();
    });
  });

  it('должен сохранять изменения записи', async () => {
    const mockRecord = {
      id: '1',
      name: 'Тестовая запись',
      description: 'Описание записи',
    };

    const updatedRecord = {
      ...mockRecord,
      name: 'Обновленная запись',
    };

    vi.mocked(pocketbaseAdmin.getRecord).mockResolvedValue(mockRecord);
    vi.mocked(pocketbaseAdmin.updateRecord).mockResolvedValue(updatedRecord);

    const onSave = vi.fn();

    render(
      <UniversalRecordEditor
        collection="promos"
        recordId="1"
        mode="modal"
        isOpen={true}
        onClose={() => {}}
        onSave={onSave}
        pbUrl="https://pb.stom-line.ru"
        token="mock-token"
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Редактирование записи')).toBeInTheDocument();
    });

    // Симулируем сохранение (это может потребовать дополнительной настройки интерфейса)
    const saveButton = screen.getByRole('button', { name: /сохранить/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(onSave).toHaveBeenCalledWith(updatedRecord);
    });
  });
});

describe('Авторизация', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('должен успешно авторизовать пользователя', async () => {
    const mockAuthResult = {
      success: true,
      data: {
        token: 'mock-token',
        record: { id: '1', email: '<EMAIL>' },
      },
    };

    vi.mocked(pocketbaseAdmin.authenticateUser).mockResolvedValue(mockAuthResult);

    const onAuth = vi.fn();

    // Здесь нужно будет создать тестовый компонент авторизации
    // или использовать AdminAuth компонент
  });

  it('должен обрабатывать ошибки авторизации', async () => {
    const mockAuthResult = {
      success: false,
      error: 'Неверный email или пароль',
    };

    vi.mocked(pocketbaseAdmin.authenticateUser).mockResolvedValue(mockAuthResult);

    // Тест обработки ошибок авторизации
  });
});
