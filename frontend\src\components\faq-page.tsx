'use client'

import { useState, useRef } from 'react'
import { motion, useInView, AnimatePresence } from 'framer-motion'
import { ChevronDown, HelpCircle, Search, MessageCircle, CheckCircle2, ArrowRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import { type FAQ } from '@/lib/api'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { BorderBeam } from '@/components/border-beam'
import { TracingBeam } from '@/components/tracing-beam'
import { EditButton } from '@/components/admin/EditButton'

interface FAQPageProps {
  faqs: FAQ[]
  isAuthenticated?: boolean;
}

// Анимация для контейнера с эффектом появления элементов по очереди
const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

// Анимация для отдельных элементов
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
}

export const FAQPage = ({ faqs, isAuthenticated = false }: FAQPageProps) => {
  const [openIndex, setOpenIndex] = useState<number | null>(0)
  const [searchTerm, setSearchTerm] = useState('')
  const containerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: true, margin: '-100px 0px' })

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  // Фильтрация FAQ по поисковому запросу
  const filteredFAQs = faqs.filter(faq => 
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (faq.answer?.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  // Группировка FAQ по категориям
  const faqsByCategory = filteredFAQs.reduce((acc, faq) => {
    const category = faq.category || 'Общие вопросы'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(faq)
    return acc
  }, {} as Record<string, FAQ[]>)

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-olive-50 via-white to-olive-50/50">
      {/* Enhanced Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <motion.div
          animate={{
            x: [0, 30, 0],
            y: [0, -20, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -left-[10%] top-[20%] h-[400px] w-[400px] rounded-full bg-gradient-to-r from-olive-300/20 to-olive-400/30 blur-[120px]"
        />
        <motion.div
          animate={{
            x: [0, -25, 0],
            y: [0, 15, 0],
            scale: [1, 0.9, 1]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
          className="absolute -right-[5%] top-[40%] h-[300px] w-[300px] rounded-full bg-gradient-to-l from-olive-400/25 to-olive-500/20 blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, 20, 0],
            y: [0, -30, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 10
          }}
          className="absolute bottom-[20%] left-[20%] h-[350px] w-[350px] rounded-full bg-gradient-to-tr from-olive-300/15 to-olive-200/25 blur-[140px]"
        />
      </div>

      {/* Grid lines */}
      <div className="pointer-events-none absolute inset-0 z-0  bg-center opacity-[0.05]" />

      <TracingBeam>
        <div className="relative w-full z-10" ref={containerRef}>
          {/* Hero section */}
          <motion.div 
            className='relative flex h-[30vh] items-center justify-center overflow-hidden md:h-[40vh]'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className=' absolute inset-0 backdrop-blur-sm' />
            <div className='relative z-10 container px-4 md:px-6'>
              <motion.div 
                initial={{ opacity: 0, y: 20 }} 
                animate={{ opacity: 1, y: 0 }} 
                transition={{ duration: 0.6 }} 
                className='text-center'
              >
                <Badge variant="flat">
                  <HelpCircle className="h-4 w-4 mr-2" />
                  Часто задаваемые вопросы
                </Badge>

                <h1 className='text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gray-900 via-olive-800 to-gray-900 bg-clip-text text-transparent mb-8'>
                  Часто задаваемые вопросы
                </h1>

                <p className='text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed'>
                  Ответы на самые распространенные вопросы о наших услугах и процедурах
                </p>
              </motion.div>
            </div>
          </motion.div>

          {/* Search section */}
          <section className="bg-gradient-to-b from-olive-50/80 to-white/80 backdrop-blur-sm py-12">
            <div className="container mx-auto px-4 md:px-6">
              <motion.div 
                className="mx-auto mb-8 max-w-2xl"
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-olive-400" />
                  <Input 
                    type="text" 
                    placeholder="Поиск по вопросам..." 
                    className="pl-10 bg-white border-olive-200 focus:border-olive-400 focus:ring-olive-400 h-12 rounded-xl shadow-sm"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </motion.div>
            </div>
          </section>

          {/* FAQ content */}
          <section className="py-12 md:py-20">
            <div className="container mx-auto px-4 md:px-6">
              {filteredFAQs.length > 0 ? (
                <div className="grid grid-cols-1 gap-8 lg:grid-cols-12">
                  {/* Categories sidebar */}
                  <motion.div 
                    className="lg:col-span-3 space-y-4"
                    initial={{ opacity: 0, x: -20 }}
                    animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <div className="sticky top-24">
                      <h3 className="text-lg font-semibold text-olive-800 mb-4">Категории</h3>
                      <div className="space-y-2">
                        {Object.keys(faqsByCategory).map((category) => (
                          <div 
                            key={`category-nav-${category}`}
                            className="flex items-center p-2 rounded-lg hover:bg-olive-50 transition-colors cursor-pointer"
                            onClick={() => {
                              const el = document.getElementById(`category-${category.replace(/\s+/g, '-')}`)
                              el?.scrollIntoView({ behavior: 'smooth', block: 'start' })
                            }}
                          >
                            <HelpCircle className="h-4 w-4 text-olive-600 mr-2" />
                            <span className="text-olive-700">{category}</span>
                            <Badge className="ml-auto bg-olive-100 text-olive-800 hover:bg-olive-200">
                              {faqsByCategory[category].length}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  </motion.div>

                  {/* FAQ items */}
                  <motion.div 
                    className="lg:col-span-9 space-y-12"
                    variants={staggerContainer}
                    initial="hidden"
                    animate={isInView ? "visible" : "hidden"}
                  >
                    {Object.entries(faqsByCategory).map(([category, categoryFaqs]) => (
                      <div 
                        key={`category-${category}`} 
                        id={`category-${category.replace(/\s+/g, '-')}`}
                        className="scroll-mt-24"
                      >
                        <motion.div 
                          variants={fadeIn}
                          className="flex items-center mb-6"
                        >
                          <div className="bg-olive-100 mr-4 flex h-10 w-10 items-center justify-center rounded-full">
                            <HelpCircle className="h-5 w-5 text-olive-600" />
                          </div>
                          <h2 className="text-2xl font-bold text-olive-800">{category}</h2>
                        </motion.div>
                        
                        <div className="space-y-4">
                          {categoryFaqs.map((faq, index) => (
                            <motion.div
                            key={faq.id}
                            variants={fadeIn}
                            className="relative rounded-xl border border-olive-100 bg-white/80 backdrop-blur-sm shadow-md overflow-hidden group"
                            >
                               <EditButton 
                                 collection="faq" 
                                 id={faq.id} 
                                 position="top-right" 
                                 size="sm"
                                 isAuthenticated={isAuthenticated}
                                 className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 border border-gray-200 shadow-sm"
                               />
                              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                <BorderBeam 
                                  colorFrom="#4D8C29" 
                                  colorTo="#85C028" 
                                  size={60} 
                                  duration={8} 
                                  delay={index * 0.2} 
                                />
                              </div>
                              <button
                                type="button"
                                onClick={() => toggleFAQ(faqs.indexOf(faq))}
                                className="flex w-full items-center justify-between p-6 text-left hover:bg-olive-50/50 transition-colors relative z-10"
                              >
                                <div className="flex items-start">
                                  <MessageCircle className="text-olive-500 mr-3 h-5 w-5 mt-1 flex-shrink-0" />
                                  <h3 className="text-lg font-medium text-olive-800">{faq.question}</h3>
                                </div>
                                <ChevronDown
                                  className={cn(
                                    'h-5 w-5 text-olive-600 transition-transform duration-300',
                                    openIndex === faqs.indexOf(faq) ? 'rotate-180 transform' : ''
                                  )}
                                />
                              </button>
                              <AnimatePresence>
                                {openIndex === faqs.indexOf(faq) && (
                                  <motion.div
                                    initial={{ height: 0, opacity: 0 }}
                                    animate={{ height: 'auto', opacity: 1 }}
                                    exit={{ height: 0, opacity: 0 }}
                                    transition={{ duration: 0.3 }}
                                    className="border-t border-olive-100 bg-olive-50/30 p-6 relative z-10"
                                  >
                                    <div 
                                      className="prose prose-olive max-w-none prose-headings:text-olive-800 prose-a:text-olive-600" 
                                      dangerouslySetInnerHTML={{ __html: faq.answer || '' }} 
                                    />
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </motion.div>
                </div>
              ) : (
                <div className="text-center py-10 bg-white/80 backdrop-blur-sm rounded-xl border border-olive-100 shadow-sm">
                  {searchTerm ? (
                    <>
                      <p className="text-gray-500 mb-2">По запросу <strong>"{searchTerm}"</strong> ничего не найдено.</p>
                      <p className="text-gray-500">Попробуйте изменить поисковый запрос или свяжитесь с нами для получения дополнительной информации.</p>
                    </>
                  ) : (
                    <p className="text-gray-500">Вопросы загружаются или отсутствуют в базе данных.</p>
                  )}
                </div>
              )}
            </div>
          </section>

          {/* CTA section */}
          <section className="bg-olive-50/80 backdrop-blur-sm py-16">
            <div className="container mx-auto px-4 md:px-6">
              <motion.div
                className="mx-auto max-w-3xl text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <h2 className="mb-6 text-3xl font-bold md:text-4xl">Не нашли ответ на свой вопрос?</h2>
                <p className="text-olive-800 mb-8 text-lg">Наши специалисты всегда готовы помочь вам и ответить на любые вопросы</p>

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {[
                    'Индивидуальная консультация',
                    'Быстрый ответ в течение 24 часов',
                    'Профессиональная помощь',
                    'Конфиденциальность информации'
                  ].map((item, index) => (
                    <div key={`cta-feature-${index}`} className="relative flex items-center overflow-hidden rounded-lg bg-white/80 backdrop-blur-sm p-4 shadow-sm">
                      <CheckCircle2 className="text-olive-600 mr-3 h-5 w-5 flex-shrink-0" />
                      <span className="text-olive-900">{item}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-8 flex flex-col gap-4 sm:flex-row justify-center">
                  <Button className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-xl">
                    Задать вопрос <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  <Button variant="outline" className="border-olive-200 text-olive-700 hover:bg-olive-50 shadow-lg">
                    Записаться на консультацию
                  </Button>
                </div>
              </motion.div>
            </div>
          </section>
        </div>
      </TracingBeam>
    </div>
  )
}
