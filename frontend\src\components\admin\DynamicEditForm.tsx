import * as React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { TiptapEditorWrapper } from './TiptapEditorWrapper';
import { MultiSelect } from '@/components/ui/multiselect';
import { Select, SelectTrigger, SelectContent, SelectItem } from '@/components/ui/select';

interface FieldSchema {
  name: string;
  type: string;
  collectionId?: string;
  label?: string; // алиас для отображения на русском
  [key: string]: unknown;
}

interface CollectionSchema {
  fields: FieldSchema[];
  [key: string]: unknown;
}

interface DynamicEditFormProps {
  collection: string;
  id: string;
  schema: CollectionSchema;
  record: Record<string, unknown>;
  pbUrl: string;
  onSuccess?: () => void;
  token?: string;
}

interface RelationOptions {
  id: string;
  name?: string;
  title?: string;
  [key: string]: unknown;
}

export const DynamicEditForm: React.FC<DynamicEditFormProps> = ({
  collection,
  id,
  schema,
  record,
  pbUrl,
  onSuccess,
  token,
}) => {
  const [form, setForm] = React.useState<Record<string, unknown>>({ ...record });
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState(false);
  const [relations, setRelations] = React.useState<Record<string, RelationOptions[]>>({});

  // Загружаем данные для relation-полей
  React.useEffect(() => {
    async function fetchRelations() {
      const rels: Record<string, RelationOptions[]> = {};
      for (const field of schema.fields) {
        if (field.type === 'relation' && typeof field.collectionId === 'string') {
          try {
            const res = await fetch(`${pbUrl}/api/collections/${field.collectionId}/records?perPage=100`);
            const data = await res.json();
            rels[field.name] = Array.isArray(data.items) ? data.items : [];
          } catch {
            rels[field.name] = [];
          }
        }
      }
      setRelations(rels);
    }
    fetchRelations();
    // eslint-disable-next-line
  }, [schema, pbUrl]);

  // Нормализация relation-массивов для мультиселекта при инициализации формы
  React.useEffect(() => {
    const normalized: Record<string, unknown> = { ...record };
    schema.fields.forEach(field => {
      if (field.type === 'relation') {
        let maxSelect = 1;
        if (field.maxSelect !== undefined && field.maxSelect !== null) {
          const parsed = Number(field.maxSelect);
          if (!Number.isNaN(parsed) && parsed > 1) maxSelect = parsed;
        }
        if (maxSelect > 1) {
          const raw = record[field.name];
          if (Array.isArray(raw)) {
            normalized[field.name] = raw.map(String);
          } else if (typeof raw === 'string' && raw.length > 0) {
            normalized[field.name] = raw.split(',').map((s: string) => s.trim()).filter(Boolean);
          } else if (raw !== undefined && raw !== null) {
            normalized[field.name] = [String(raw)];
          } else {
            normalized[field.name] = [];
          }
        }
      }
    });
    setForm(normalized);
    // eslint-disable-next-line
  }, [record, schema]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox' && e.target instanceof HTMLInputElement) {
      setForm((prev) => ({ ...prev, [name]: (e.target as HTMLInputElement).checked }));
    } else {
      setForm((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setForm((prev) => ({ ...prev, [name]: checked }));
  };

  // Обработка file input
  const handleFileChange = (fieldName: string, fileList: FileList | null) => {
    if (fileList && fileList.length > 0) {
      setForm((prev) => ({ ...prev, [fieldName]: fileList[0] }));
    }
  };

  const fetchWithAuth = (url: string, options: RequestInit = {}) => {
    let headers: HeadersInit = options.headers || {};
    if (token) {
      if (headers instanceof Headers) {
        headers.set('Authorization', `Bearer ${token}`);
      } else if (Array.isArray(headers)) {
        headers.push(['Authorization', `Bearer ${token}`]);
      } else {
        headers = { ...headers, Authorization: `Bearer ${token}` };
      }
    }
    return fetch(url, { ...options, headers });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);
    try {
      let res: Response;
      const hasFile = schema.fields.some(f => f.type === 'file' && form[f.name] instanceof File);
      // --- сериализация relation-массивов ---
      const dataToSend: Record<string, unknown> = { ...form };
      schema.fields.forEach(field => {
        if (field.type === 'relation') {
          let maxSelect = 1;
          if (field.maxSelect !== undefined && field.maxSelect !== null) {
            const parsed = Number(field.maxSelect);
            if (!Number.isNaN(parsed) && parsed > 1) maxSelect = parsed;
          }
          if (maxSelect > 1) {
            // сериализуем массив в строку через запятую
            const val = form[field.name];
            if (Array.isArray(val)) {
              dataToSend[field.name] = val.join(',');
            } else if (typeof val === 'string') {
              dataToSend[field.name] = val;
            } else {
              dataToSend[field.name] = '';
            }
          }
        }
      });
      if (hasFile) {
        const formData = new FormData();
        for (const field of schema.fields) {
          if (field.type === 'file' && form[field.name] instanceof File) {
            formData.append(field.name, form[field.name] as File);
          } else if (dataToSend[field.name] !== undefined && dataToSend[field.name] !== null) {
            formData.append(field.name, String(dataToSend[field.name]));
          }
        }
        res = await fetchWithAuth(`${pbUrl}/api/collections/${collection}/records/${id}`, {
          method: 'PATCH',
          body: formData,
        });
      } else {
        res = await fetchWithAuth(`${pbUrl}/api/collections/${collection}/records/${id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(dataToSend),
        });
      }
      if (res.ok) {
        setSuccess(true);
        if (onSuccess) onSuccess();
      } else {
        const data = await res.json();
        setError(data?.message || 'Ошибка при сохранении');
      }
    } catch (e) {
      setError(e instanceof Error ? e.message : String(e));
    } finally {
      setLoading(false);
    }
  };

  return (
    <form className="space-y-4" onSubmit={handleSubmit}>
      {schema.fields?.map((field) => {
        const inputId = `field-${field.name}`;
        const label = field.label || field.name;
        if (field.type === 'editor') {
          return (
            <div key={field.name}>
              <label htmlFor={inputId} className="block font-medium mb-1">{label}</label>
              <TiptapEditorWrapper
                id={inputId}
                value={String(form[field.name] ?? '')}
                onChange={val => setForm(prev => ({ ...prev, [field.name]: val }))}
                readOnly={loading}
                style={{ minHeight: 120 }}
              />
            </div>
          );
        }
        if (field.type === 'text') {
          return (
            <div key={field.name}>
              <label htmlFor={inputId} className="block font-medium mb-1">{label}</label>
              <Input
                id={inputId}
                name={field.name}
                value={String(form[field.name] ?? '')}
                onChange={handleChange}
                disabled={loading}
              />
            </div>
          );
        }
        if (field.type === 'number') {
          return (
            <div key={field.name}>
              <label htmlFor={inputId} className="block font-medium mb-1">{label}</label>
              <Input
                id={inputId}
                type="number"
                name={field.name}
                value={form[field.name] !== undefined ? String(form[field.name]) : ''}
                onChange={handleChange}
                disabled={loading}
              />
            </div>
          );
        }
        if (field.type === 'bool') {
          return (
            <div key={field.name} className="flex items-center gap-2">
              <Checkbox
                id={inputId}
                name={field.name}
                checked={!!form[field.name]}
                onCheckedChange={(checked: boolean) => handleCheckboxChange(field.name, checked)}
                disabled={loading}
              />
              <label htmlFor={inputId}>{label}</label>
            </div>
          );
        }
        if (field.type === 'relation') {
          const options = relations[field.name] || [];
          // Универсальное определение мультиселекта
          let maxSelect = 1;
          if (field.maxSelect !== undefined && field.maxSelect !== null && field.maxSelect !== 'null') {
            const parsed = Number(field.maxSelect);
            if (!Number.isNaN(parsed)) maxSelect = parsed;
          }
          const raw = form[field.name];
          const isMulti = maxSelect > 1 || Array.isArray(raw);
          if (isMulti) {
            // Нормализуем initial value: всегда массив строк
            let selected: string[] = [];
            if (Array.isArray(raw)) {
              selected = raw.map(String);
            } else if (typeof raw === 'string' && raw.length > 0) {
              selected = raw.split(',').map(s => s.trim()).filter(Boolean);
            } else if (raw !== undefined && raw !== null) {
              selected = [String(raw)];
            } else {
              selected = [];
            }
            return (
              <div key={field.name}>
                <label htmlFor={inputId} className="block font-medium mb-1">{label}</label>
                <MultiSelect
                  id={inputId}
                  name={field.name}
                  options={options.map(opt => ({ value: opt.id, label: opt.name || opt.title || opt.id }))}
                  value={selected}
                  onChange={values => setForm(prev => ({ ...prev, [field.name]: Array.isArray(values) ? values.map(String) : [] }))}
                  disabled={loading}
                  placeholder="Выберите..."
                />
              </div>
            );
          }
          // Одиночная связь
          return (
            <div key={field.name}>
              <label htmlFor={inputId} className="block font-medium mb-1">{label}</label>
              <Select
                value={form[field.name] !== undefined && form[field.name] !== null && form[field.name] !== '' ? String(form[field.name]) : '__empty__'}
                onValueChange={val => setForm(prev => ({ ...prev, [field.name]: val === '__empty__' ? '' : val }))}
                disabled={loading}
              >
                <SelectTrigger className="w-full" id={inputId} />
                <SelectContent>
                  <SelectItem value="__empty__">-- Не выбрано --</SelectItem>
                  {options.map((opt) => (
                    <SelectItem key={opt.id} value={opt.id}>
                      {opt.name || opt.title || opt.id}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          );
        }
        if (field.type === 'file') {
          const fileValue = form[field.name];
          // Определяем url для превью (если это строка)
          let previewUrl: string | undefined = undefined;
          if (typeof fileValue === 'string' && fileValue) {
            // PocketBase files обычно доступны по pbUrl + /api/files/...
            previewUrl = fileValue.startsWith('http') ? fileValue : `${pbUrl}/api/files/${collection}/${id}/${fileValue}`;
          }
          return (
            <div key={field.name}>
              <label htmlFor={inputId} className="block font-medium mb-1">{label}</label>
              <Input
                id={inputId}
                name={field.name}
                type="file"
                onChange={e => handleFileChange(field.name, e.target.files)}
                disabled={loading}
              />
              {previewUrl && (
                <div className="mt-2">
                  <a href={previewUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">Скачать файл</a>
                  {previewUrl.match(/\.(jpg|jpeg|png|gif|webp)$/i) && (
                    <img src={previewUrl} alt={field.name} className="mt-2 max-h-40 rounded border" />
                  )}
                </div>
              )}
            </div>
          );
        }
        // TODO: date и др.
        return null;
      })}
      {error && <div className="text-red-600">{error}</div>}
      {success && <div className="text-green-600">Сохранено!</div>}
      <Button type="submit" disabled={loading}>
        {loading ? 'Сохраняю...' : 'Сохранить'}
      </Button>
    </form>
  );
};
