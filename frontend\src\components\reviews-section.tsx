'use client'

import { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { Star, Quote } from 'lucide-react'
import { type Review } from '@/lib/api'

interface ReviewsSectionProps {
  reviews: Review[]
}

export default function ReviewsSection({ reviews }: ReviewsSectionProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: true, margin: '-100px 0px' })

  return (
    <section ref={containerRef} className="bg-gradient-to-br from-olive-50 via-white to-olive-50/50 py-20">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <h2 className="mb-4 text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 via-olive-800 to-gray-900 bg-clip-text text-transparent">Отзывы наших пациентов</h2>
          <p className="text-lg text-gray-600">
            Узнайте, что говорят о нас те, кто уже воспользовался нашими услугами
          </p>
        </div>

        {reviews.length > 0 ? (
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {reviews.map((review, index) => (
              <motion.div
                key={review.id}
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="relative rounded-xl bg-gradient-to-br from-white via-white to-olive-50/30 p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-olive-200/50"
              >
                <div className="mb-4 flex items-center">
                  <div className="mr-4 h-12 w-12 overflow-hidden rounded-full bg-olive-100">
                    {review.image ? (
                      <img
                        src={`${import.meta.env.PUBLIC_API_URL || 'https://pb.stom-line.ru'}/api/files/reviews/${review.id}/${review.image}`}
                        alt={review.author}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center bg-olive-200 text-olive-600">
                        {review.author.charAt(0).toUpperCase()}
                      </div>
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold">{review.author}</h3>
                    <p className="text-sm text-gray-500">
                      {review.date ? new Date(review.date).toLocaleDateString('ru-RU') : ''}
                    </p>
                  </div>
                </div>

                <div className="mb-2 flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="h-4 w-4 text-yellow-400" fill="#facc15" />
                  ))}
                </div>

                {review.title && <h4 className="mb-2 font-medium">{review.title}</h4>}

                <Quote className="absolute right-6 top-6 h-8 w-8 text-olive-100" />

                <div className="prose prose-olive max-w-none" dangerouslySetInnerHTML={{ __html: review.content || '' }} />
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-10">
            <p className="text-gray-500">Отзывы загружаются или отсутствуют в базе данных.</p>
          </div>
        )}
      </div>
    </section>
  )
}
