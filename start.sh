#!/bin/bash

# Скрипт для запуска проекта

# Проверяем, установлен ли Docker
if ! command -v docker &> /dev/null; then
    echo "Docker не установлен. Пожалуйста, установите Docker и Docker Compose."
    exit 1
fi

# Проверяем, установлен ли Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose не установлен. Пожалуйста, установите Docker Compose."
    exit 1
fi

# Запускаем основные сервисы
echo "Запуск основных сервисов (PocketBase, MeiliSearch, Frontend)..."
docker-compose up -d pocketbase meilisearch frontend

# Ждем, пока сервисы запустятся
echo "Ожидаем запуска сервисов..."
sleep 30

# Запускаем инициализацию MeiliSearch
echo "Запуск инициализации MeiliSearch..."
docker-compose up init-meilisearch

echo "Проект успешно запущен!"
echo "Frontend: http://localhost:4321"
echo "PocketBase: http://localhost:8090"
echo "MeiliSearch: http://localhost:7700"
