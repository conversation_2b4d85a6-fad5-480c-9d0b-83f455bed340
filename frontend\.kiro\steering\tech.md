# Technology Stack

## Framework & Build System
- **Astro 5.9.2** - Main framework with SSR (server-side rendering)
- **Node.js adapter** in standalone mode
- **Vite** - Build tool and dev server
- **TypeScript** - Primary language with strict configuration

## Frontend Technologies
- **React 19** - Component library for interactive elements
- **Tailwind CSS 4** - Utility-first CSS framework with custom design system
- **Framer Motion** - Animation library
- **Radix UI** - Accessible component primitives
- **Lucide React** - Icon library

## Backend & Data
- **PocketBase** - Backend-as-a-Service for data management
- **MeiliSearch** - Search engine integration
- Authentication via PocketBase with middleware

## Content Management
- **TiptapEditor** - Rich text editor for admin panel
- **MDX** support for markdown content
- File uploads and media management

## Development Tools
- **Prettier** - Code formatting with Astro plugin
- **Vitest** - Testing framework with jsdom
- **Docker** - Containerization support

## Common Commands

### Development
```bash
# Start development server
npm run dev
# or
bun dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Search Management
```bash
# Sync search indexes
npm run sync-search

# Initialize search
npm run init-search
```

### Testing
```bash
# Run tests
npm test
# or
vitest
```

### Docker
```bash
# Build container
docker build -t astro-app .

# Run container
docker run -p 4321:4321 astro-app
```

## Environment Variables
- `PUBLIC_API_URL` - PocketBase backend URL
- `PUBLIC_SEARCH_URL` - MeiliSearch instance URL
- `PUBLIC_SEARCH_API_KEY` - MeiliSearch API key
- `PUBLIC_POCKETBASE_EMAIL` - Admin email for PocketBase
- `PUBLIC_POCKETBASE_PASSWORD` - Admin password for PocketBase