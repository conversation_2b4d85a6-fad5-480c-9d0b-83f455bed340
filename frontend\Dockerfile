FROM node:22-alpine AS build

WORKDIR /app

# Копируем файлы package.json и package-lock.json (если есть)
COPY package*.json ./

# Устанавливаем зависимости
RUN npm install

# Копируем исходный код
COPY . .

# Собираем приложение
RUN npm run build

# Этап запуска
FROM node:22-alpine AS runtime

WORKDIR /app

# Копируем собранные файлы и необходимые зависимости
COPY --from=build /app/dist ./dist
COPY --from=build /app/node_modules ./node_modules
COPY --from=build /app/package.json ./

# Устанавливаем переменные окружения для продакшена
ENV HOST=0.0.0.0
ENV PORT=4321
ENV NODE_ENV=production
# ENV PUBLIC_API_URL=http://backend:1337
# Токен API будет передаваться через переменные окружения при запуске
# ENV STRAPI_API_TOKEN=your_token_here

# Открываем порт
EXPOSE 4321

# Запускаем приложение
CMD ["node", "./dist/server/entry.mjs"]
