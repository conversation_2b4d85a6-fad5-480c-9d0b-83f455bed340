'use client'

import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'

interface TypographyProps {
  children: React.ReactNode
  className?: string
}

// Заголовки
const H1 = ({ children, className }: TypographyProps) => (
  <h1 className={cn(
    'text-3xl font-bold tracking-tighter text-gray-900',
    'md:text-4xl lg:text-5xl xl:text-6xl',
    className
  )}>
    {children}
  </h1>
)

const H2 = ({ children, className }: TypographyProps) => (
  <h2 className={cn(
    'text-2xl font-bold tracking-tight text-gray-900',
    'md:text-3xl lg:text-4xl',
    className
  )}>
    {children}
  </h2>
)

// Акцентный текст с градиентом
const AccentText = ({ children, className }: TypographyProps) => (
  <motion.div
    className={cn(
      'mt-2 bg-gradient-to-r from-[#4E8C29] to-[#8BC34A] bg-clip-text text-transparent',
      className
    )}
    whileHover={{ 
      scale: 1.02,
      transition: { duration: 0.3 }
    }}
  >
    {children}
  </motion.div>
)

// Основной текст
const Paragraph = ({ children, className }: TypographyProps) => (
  <p className={cn(
    'text-base text-gray-800 leading-relaxed',
    'md:text-lg md:leading-relaxed',
    className
  )}>
    {children}
  </p>
)

// Мелкий текст
const Small = ({ children, className }: TypographyProps) => (
  <p className={cn(
    'text-sm text-gray-600 leading-relaxed',
    className
  )}>
    {children}
  </p>
)

export { H1, H2, AccentText, Paragraph, Small }