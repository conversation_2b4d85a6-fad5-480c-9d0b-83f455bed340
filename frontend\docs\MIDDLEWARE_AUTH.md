# Middleware авторизации для Astro

## Описание

Реализован middleware для Astro, который проверяет авторизацию пользователя на серверной стороне и записывает состояние авторизации в `Astro.locals`. Это позволяет условно отображать кнопки редактирования и другие элементы интерфейса администратора.

## Компоненты системы

### 1. Основной middleware (`src/middleware.ts`)

Основной файл middleware, который:
- Извлекает токен из cookies или заголовков Authorization
- Проверяет валидность токена через PocketBase API
- Записывает результат проверки в `Astro.locals`
- Поддерживает как обычных пользователей, так и суперпользователей (админов)

### 2. Типы (`src/env.d.ts`)

Определены типы для `Astro.locals`:
```typescript
declare namespace App {
  interface Locals {
    isAuthenticated: boolean;
    user?: {
      id: string;
      email: string;
      token: string;
      type: 'admin' | 'user';
    };
  }
}
```

### 3. Утилиты авторизации (`src/middleware/auth.ts`)

Вспомогательные функции для работы с авторизацией:
- `isUserAuthenticated(locals)` - проверка авторизации
- `getCurrentUser(locals)` - получение данных пользователя
- `isAdmin(locals)` - проверка прав администратора

### 4. Компонент синхронизации (`src/components/admin/AuthSync.tsx`)

React компонент, который:
- Синхронизирует токен между localStorage и cookies
- Отслеживает изменения токена в localStorage
- Автоматически обновляет cookies для middleware

### 5. Серверный компонент кнопки редактирования (`src/components/admin/EditButtonServer.astro`)

Astro компонент, который:
- Использует `Astro.locals` для проверки авторизации
- Отображает кнопку редактирования только авторизованным пользователям
- Работает на серверной стороне без JavaScript

## Как это работает

1. **Авторизация пользователя:**
   - Пользователь вводит логин/пароль в форме авторизации
   - Токен сохраняется в localStorage
   - Компонент AuthSync автоматически копирует токен в cookies

2. **Проверка на сервере:**
   - При каждом запросе middleware извлекает токен из cookies
   - Проверяет валидность токена через PocketBase API
   - Записывает результат в `Astro.locals`

3. **Условное отображение:**
   - Серверные компоненты используют `Astro.locals.isAuthenticated`
   - Кнопки редактирования отображаются только авторизованным пользователям

## Использование

### В Astro компонентах:

```astro
---
import { isUserAuthenticated } from '@/middleware/auth';
import EditButtonServer from '@/components/admin/EditButtonServer.astro';

const isAuth = isUserAuthenticated(Astro.locals);
---

{isAuth && (
  <EditButtonServer collection="pages" id="some-id" />
)}
```

### В React компонентах:

Обновленные компоненты автоматически синхронизируют токены:
- `AdminEditPage` - устанавливает cookies при авторизации
- `AuthSync` - следит за изменениями токена

## Тестирование

1. **Страница тестирования:** `/admin/test-auth`
2. **Страница статуса:** `/admin/login`
3. **Пример использования:** `/about` (с кнопкой редактирования)

## Логи

Middleware выводит в консоль сервера:
- "Токен не найден в запросе" - когда токен отсутствует
- "Токен недействителен" - когда токен не прошел проверку
- "Пользователь авторизован: email" - при успешной авторизации

## Безопасность

- Токены передаются через secure cookies
- Проверка токенов происходит на сервере
- Поддержка как обычных пользователей, так и администраторов
- Автоматическое обновление токенов через PocketBase API
