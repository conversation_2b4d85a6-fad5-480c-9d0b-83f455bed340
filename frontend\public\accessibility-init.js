// Скрипт для применения классов accessibility к <body> до загрузки стилей и React
(function () {
  function applyAccessibilityClasses() {
    try {
      var mode = localStorage.getItem('accessibility-mode');
      var fontSize = localStorage.getItem('accessibility-font-size');
      var contrast = localStorage.getItem('accessibility-contrast');
      var body = document.body;
      
      // Очищаем все классы доступности
      body.classList.remove('accessibility', 'font-large', 'font-extra-large', 'contrast-high', 'contrast-inverted');
      
      // Применяем классы если режим доступности включен
      if (mode === 'true') {
        body.classList.add('accessibility');
      }
      
      // Применяем размер шрифта независимо от режима доступности
      if (fontSize === 'large') {
        body.classList.add('font-large');
      } else if (fontSize === 'extra-large') {
        body.classList.add('font-extra-large');
      }
      
      // Применяем контрастность независимо от режима доступности
      if (contrast === 'high') {
        body.classList.add('contrast-high');
      } else if (contrast === 'inverted') {
        body.classList.add('contrast-inverted');
      }
    } catch (e) {
      console.warn('Ошибка применения настроек доступности:', e);
    }
  }

  // Применяем классы сразу при загрузке
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyAccessibilityClasses);
  } else {
    applyAccessibilityClasses();
  }

  // Применяем классы при навигации Astro
  window.addEventListener('astro:before-preparation', applyAccessibilityClasses);
  window.addEventListener('astro:after-swap', applyAccessibilityClasses);
  
  // Применяем классы при изменении в localStorage (для синхронизации между вкладками)
  window.addEventListener('storage', function(e) {
    if (e.key && e.key.startsWith('accessibility-')) {
      applyAccessibilityClasses();
    }
  });
})();
