---
import Layout from '../../layouts/Layout.astro';
import PocketBase from 'pocketbase';
import { Button } from '../../components/ui/button';
import { CalendarIcon, PhoneIcon, MapPinIcon, TagIcon } from 'lucide-react';
import { generateServiceJsonLd } from '../../lib/seo-config';
import RDFaMarkup from '../../components/RDFaMarkup.astro';

// URL API
const PUBLIC_API_URL = 'https://pb.stom-line.ru';

export async function getStaticPaths() {
  const pb = new PocketBase(PUBLIC_API_URL);

  try {
    const services = await pb.collection('services').getFullList({
      expand: 'category',
    });

    return services.map((service) => ({
      params: { slug: service.slug || service.id },
      props: { service },
    }));
  } catch (error) {
    console.error('Ошибка при получении данных об услугах:', error);
    return [];
  }
}

const { service } = Astro.props;

// Проверяем, что service определен
if (!service) {
  return Astro.redirect('/services');
}

// Получаем URL изображения, если есть
let imageUrl = '';
if (service?.image) {
  try {
    imageUrl = `${PUBLIC_API_URL}/api/files/${service.collectionId || 'pbc_services'}/${service.id}/${service.image}`;
  } catch (error) {
    console.error('Ошибка при получении URL изображения:', error);
  }
}

// Получаем категорию
const category = service?.expand?.category;

// Получаем связанные цены
const pb = new PocketBase(PUBLIC_API_URL);
let prices = [];
try {
  if (service?.id) {
    prices = await pb.collection('prices').getFullList({
      filter: `service="${service.id}"`,
      sort: 'sort_order',
    });
  }
} catch (error) {
  console.error('Ошибка при получении цен:', error);
}

// Получаем связанных специалистов
let doctors = [];
try {
  const allDoctors = await pb.collection('doctors').getFullList({
    expand: 'services',
  });

  if (service?.id) {
    doctors = allDoctors.filter(doctor =>
      doctor.expand?.services?.some(s => s.id === service.id)
    );
  }
} catch (error) {
  console.error('Ошибка при получении специалистов:', error);
}

// SEO данные
const serviceName = service?.name || 'Услуга';
const title = service?.meta_title || `${serviceName} | Стоматологические услуги STOM-LINE в Мурманске`;
const description = service?.meta_description || `${serviceName} в стоматологической клинике STOM-LINE. ${service?.short_description?.replace(/<[^>]*>/g, '') || 'Качественное лечение по доступным ценам.'} Записаться на прием: +7 (8152) 52-57-08`;
const image = imageUrl || 'https://stom-line.ru/og-image.jpg';

// Генерируем структурированные данные для услуги
const serviceJsonLd = generateServiceJsonLd(service);

// Формируем ключевые слова
const keywords = [
  serviceName,
  'стоматологические услуги',
  'стоматология Мурманск',
  'STOM-LINE',
  category?.name,
  'лечение зубов'
].filter(Boolean);
---

<Layout
  title={title}
  description={description}
  keywords={keywords}
  image={image}
  type="service"
  jsonLd={serviceJsonLd}
>
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-5xl mx-auto">
      <!-- Хлебные крошки -->
      <div class="text-sm text-olive-500 mb-6">
        <a href="/" class="hover:text-olive-700">Главная</a>
        <span class="mx-2">/</span>
        <a href="/services" class="hover:text-olive-700">Услуги</a>
        {category && (
          <>
            <span class="mx-2">/</span>
            <span class="text-olive-700">{category.name}</span>
          </>
        )}
        <span class="mx-2">/</span>
        <span class="text-olive-700">{service.name}</span>
        <a href={`/admin/edit/services/${service.id}`} target="_blank" rel="noopener noreferrer" class="ml-4 text-xs text-blue-600 underline">Редактировать</a>
      </div>

      <!-- Основная информация об услуге -->
      <div class="bg-white rounded-xl shadow-md overflow-hidden mb-10">
        <div class="md:flex">
          {imageUrl && (
            <div class="md:flex-shrink-0">
              <img
                src={imageUrl}
                alt={service.name}
                class="h-64 w-full object-cover md:w-64 md:h-auto"
              />
            </div>
          )}

          <div class="p-8">
            <h1 class="text-3xl font-bold text-olive-800 mb-4">{service.name}</h1>

            {service.short_description && (
              <div class="text-olive-700 mb-6" set:html={service.short_description} />
            )}

            {category && (
              <div class="flex items-center gap-2 mb-6">
                <TagIcon className="h-4 w-4 text-olive-600" />
                <span class="text-olive-600">Категория: {category.name}</span>
              </div>
            )}

            <div class="flex flex-col sm:flex-row gap-4">
              <Button className="bg-olive-600 hover:bg-olive-700 text-white flex items-center gap-2">
                <PhoneIcon className="h-4 w-4" />
                <a href="tel:+78152525708" class="inline-block">Записаться на прием</a>
              </Button>

              <Button variant="outline" className="border-olive-600 text-olive-700 hover:bg-olive-50 flex items-center gap-2">
                <a href="/prices" class="inline-block">Посмотреть все цены</a>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- Подробное описание услуги -->
      {service.content && (
        <div class="bg-white rounded-xl shadow-md overflow-hidden p-8 mb-10">
          <h2 class="text-2xl font-semibold text-olive-800 mb-4">Описание услуги</h2>
          <div class="prose prose-olive max-w-none" set:html={service.content} />
        </div>
      )}

      <!-- Цены на услугу -->
      {prices.length > 0 && (
        <div class="bg-white rounded-xl shadow-md overflow-hidden p-8 mb-10">
          <h2 class="text-2xl font-semibold text-olive-800 mb-4">Стоимость услуги</h2>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-olive-200">
              <thead class="bg-olive-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-olive-700 uppercase tracking-wider">
                    Наименование
                  </th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-olive-700 uppercase tracking-wider">
                    Цена
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-olive-100">
                {prices.map((price) => (
                  <tr class="hover:bg-olive-50">
                    <td class="px-6 py-4 whitespace-normal text-sm text-olive-800">
                      {price.name}
                      {price.description && (
                        <p class="text-xs text-olive-500 mt-1">{price.description}</p>
                      )}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right">
                      {price.old_price > 0 && (
                        <span class="line-through text-olive-400 mr-2">{price.old_price} ₽</span>
                      )}
                      <span class="font-medium text-olive-800">{price.price} ₽</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <p class="text-sm text-olive-500 mt-4">
            * Окончательная стоимость определяется после консультации и диагностики
          </p>
        </div>
      )}

      <!-- Специалисты, выполняющие услугу -->
      {doctors.length > 0 && (
        <div class="bg-white rounded-xl shadow-md overflow-hidden p-8 mb-10">
          <h2 class="text-2xl font-semibold text-olive-800 mb-4">Наши специалисты</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {doctors.map((doctor) => {
              const fullName = `${doctor.surname} ${doctor.name} ${doctor.patronymic || ''}`.trim();
              let photoUrl = '';
              if (doctor.photo) {
                photoUrl = `${PUBLIC_API_URL}/api/files/${doctor.collectionId}/${doctor.id}/${doctor.photo}`;
              }

              return (
                <a
                  href={`/specialists/${doctor.slug || doctor.id}`}
                  class="flex items-center gap-4 p-4 border border-olive-100 rounded-lg hover:bg-olive-50 transition-colors"
                >
                  {photoUrl ? (
                    <img
                      src={photoUrl}
                      alt={fullName}
                      class="h-16 w-16 object-cover rounded-full"
                    />
                  ) : (
                    <div class="h-16 w-16 bg-olive-100 rounded-full flex items-center justify-center">
                      <span class="text-olive-400 text-xs">Фото</span>
                    </div>
                  )}

                  <div>
                    <h3 class="font-medium text-olive-800">{fullName}</h3>
                    {doctor.position && (
                      <p class="text-sm text-olive-600">{doctor.position}</p>
                    )}
                  </div>
                </a>
              );
            })}
          </div>
        </div>
      )}

      <!-- Контактная информация -->
      <div class="bg-olive-50 rounded-xl shadow-sm overflow-hidden p-8">
        <h2 class="text-2xl font-semibold text-olive-800 mb-4">Контактная информация</h2>

        <div class="flex flex-col gap-4">
          <div class="flex items-start gap-3">
            <PhoneIcon className="h-5 w-5 text-olive-600 mt-0.5" />
            <div>
              <p class="font-medium text-olive-800">Телефон для записи</p>
              <a href="tel:+78152525708" class="text-olive-600 hover:text-olive-800">+7 (8152) 52-57-08</a>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <MapPinIcon className="h-5 w-5 text-olive-600 mt-0.5" />
            <div>
              <p class="font-medium text-olive-800">Адрес клиники</p>
              <p class="text-olive-600">г. Мурманск, ул. Полярные Зори, д. 35/2</p>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <CalendarIcon className="h-5 w-5 text-olive-600 mt-0.5" />
            <div>
              <p class="font-medium text-olive-800">Часы работы</p>
              <p class="text-olive-600">Пн-Пт: 9:00-20:00, Сб: 9:00-18:00, Вс: выходной</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- RDFa разметка для услуги -->
  <RDFaMarkup type="service" data={service} />
</Layout>
