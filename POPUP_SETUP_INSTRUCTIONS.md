# Инструкция по настройке акционного поп-апа

## Обзор

Реализован акционный поп-ап, который показывается при:
- Попытке покинуть сайт (движение мыши к верхней части экрана)
- Прокрутке 50% страницы
- С ограничением показа (не чаще 1 раза в 24 часа)

## Как это работает

1. **Специальная акция**: Поп-ап использует акцию с slug `popup-promo` из коллекции `promos`
2. **Автоматическое отображение**: Если акция существует и активна (`is_active = true`), поп-ап будет показан
3. **Ограничения**: Поп-ап не показывается повторно в течение 24 часов (настраивается)

## Создание акции для поп-апа

### Через админ-панель (рекомендуется)

1. Перейдите в админ-панель: `http://localhost:4321/admin/promos`
2. Нажмите "Создать новую запись"
3. Заполните поля:
   - **Title**: `Только сегодня!`
   - **Subtitle**: `Запишитесь на бесплатный осмотр — получите скидку 10% на первое лечение.`
   - **Slug**: `popup-promo` (обязательно!)
   - **Content**: 
     ```html
     <div class="text-center">
       <h2 class="text-2xl font-bold text-olive-800 mb-4">Только сегодня!</h2>
       <p class="text-lg text-olive-700 mb-6">
         Запишитесь на бесплатный осмотр — получите скидку 10% на первое лечение.
       </p>
       <div class="bg-olive-50 p-4 rounded-lg mb-6">
         <p class="text-olive-800 font-semibold">
           🎁 Что входит в бесплатный осмотр:
         </p>
         <ul class="text-olive-700 mt-2 text-left">
           <li>• Консультация врача-стоматолога</li>
           <li>• Составление плана лечения</li>
           <li>• Рекомендации по уходу за полостью рта</li>
         </ul>
       </div>
       <p class="text-sm text-olive-600">
         ⏰ Предложение действует только сегодня!
       </p>
     </div>
     ```
   - **Is Active**: ✅ (включено)
   - **Is Featured**: ❌ (выключено, чтобы не показывалось в обычных списках акций)
   - **Sort Order**: `999` (в конце списка)
   - **Meta Title**: `Специальное предложение - Бесплатный осмотр`
   - **Meta Description**: `Запишитесь на бесплатный осмотр и получите скидку 10% на первое лечение`

4. Сохраните запись

### Через SQL (альтернативный способ)

Если нужно добавить через SQL, используйте файл `backend/add-popup-promo.sql`

## Управление поп-апом

### Включить/выключить поп-ап
- Измените поле `is_active` у акции с slug `popup-promo`
- `true` - поп-ап активен
- `false` - поп-ап отключен

### Изменить содержимое
- Отредактируйте поля `title`, `subtitle`, `content` у акции с slug `popup-promo`

### Настройки показа
Настройки находятся в файле `frontend/src/components/promo-popup-wrapper.tsx`:

```typescript
const { isVisible, closePopup } = usePromoPopup({
  promo,
  scrollThreshold: 50, // Показать при прокрутке 50%
  showOnExit: true, // Показать при попытке покинуть сайт
  showOnScroll: true, // Показать при прокрутке
  cooldownHours: 24 // Не показывать повторно 24 часа
})
```

## Тестирование

1. Создайте акцию с slug `popup-promo` и `is_active = true`
2. Откройте сайт в браузере
3. Прокрутите страницу на 50% или попробуйте закрыть вкладку
4. Поп-ап должен появиться

### Сброс ограничений для тестирования
Очистите localStorage в браузере:
```javascript
localStorage.removeItem('promo-popup-timestamp')
```

## Файлы проекта

- `frontend/src/components/promo-popup.tsx` - Компонент поп-апа
- `frontend/src/components/promo-popup-wrapper.tsx` - Обертка для интеграции с Astro
- `frontend/src/hooks/use-promo-popup.ts` - Хук для управления логикой показа
- `frontend/src/lib/api.ts` - Функция `getPopupPromo()` для получения акции
- `frontend/src/layouts/Layout.astro` - Интеграция в основной layout

## Возможные улучшения

1. **Таймер обратного отсчета**: Добавить реальный таймер до конца акции
2. **A/B тестирование**: Разные варианты поп-апов
3. **Аналитика**: Отслеживание конверсий поп-апа
4. **Персонализация**: Разные поп-апы для разных страниц
5. **Анимации**: Более сложные анимации появления
