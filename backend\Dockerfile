FROM alpine:latest

ARG PB_VERSION=0.28.4

RUN apk add --no-cache \
    unzip \
    ca-certificates

# download and unzip PocketBase
ADD https://github.com/pocketbase/pocketbase/releases/download/v${PB_VERSION}/pocketbase_${PB_VERSION}_linux_amd64.zip /tmp/pb.zip
RUN unzip /tmp/pb.zip -d /pb/

# Создаем директории для данных и хуков
RUN mkdir -p /pb/pb_data /pb/pb_hooks /pb/pb_public

# Копируем хуки в образ (они будут включены в образ для деплоя)
COPY hooks/ /pb/pb_hooks/

# Устанавливаем правильные права доступа для хуков
RUN chmod -R 755 /pb/pb_hooks

# НЕ копируем данные в образ - они должны быть в volume
# Данные будут монтироваться из внешнего volume при запуске контейнера

# uncomment to copy the local pb_migrations dir into the image
# COPY ./pb_migrations /pb/pb_migrations

# Создаем volume для данных (данные не будут перезатираться при пересборке)
VOLUME ["/pb/pb_data"]

EXPOSE 8090

# start PocketBase
CMD ["/pb/pocketbase", "serve", "--http=0.0.0.0:8090"]