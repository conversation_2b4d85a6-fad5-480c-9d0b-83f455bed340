{"collections": [{"name": "callback_requests", "type": "base", "system": false, "schema": [{"name": "name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 2, "max": 100, "pattern": ""}}, {"name": "phone", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": 10, "max": 20, "pattern": "^[+]?[0-9\\s\\-\\(\\)]{10,20}$"}}, {"name": "message", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": 0, "max": 1000, "pattern": ""}}, {"name": "status", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["new", "in_progress", "completed", "cancelled"]}}, {"name": "source", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": 0, "max": 100, "pattern": ""}}, {"name": "ip_address", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": 0, "max": 45, "pattern": ""}}, {"name": "user_agent", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": 0, "max": 500, "pattern": ""}}, {"name": "processed_at", "type": "date", "system": false, "required": false, "unique": false, "options": {"min": "", "max": ""}}, {"name": "processed_by", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": 0, "max": 100, "pattern": ""}}, {"name": "notes", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": 0, "max": 2000, "pattern": ""}}], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}]}