"use client";
import { useEffect } from "react";
import { motion, stagger, useAnimate } from "motion/react";
import { cn } from "@/lib/utils";

export const TextGenerateEffect = ({
  words,
  className,
  filter = true,
  duration = 0.5,
}: {
  words: string;
  className?: string;
  filter?: boolean;
  duration?: number;
}) => {
  const [scope, animate] = useAnimate();
  let wordsArray = words.split(" ");
  useEffect(() => {
    animate(
      "span",
      {
        opacity: 1,
        filter: filter ? "blur(0px)" : "none",
      },
      {
        duration: duration ? duration : 1,
        delay: stagger(0.1),
      }
    );
  }, [scope.current]);

  const renderWords = () => {
    return (
      <motion.div ref={scope}>
        {wordsArray.map((word, idx) => {
          const isSpecialWord = word === "улыбка" || word === "гордость";
          const isSloganSpecialWord = word === "нечего" || word === "бояться";
          return (
            <motion.span
              key={word + idx}
              className={cn(
                "opacity-0",
                isSpecialWord || isSloganSpecialWord
                  ? "bg-gradient-to-r from-[#8BC34A] to-[#7CB342] bg-clip-text text-transparent font-bold"
                  : "text-gray-900"
              )}
              style={{
                filter: filter ? "blur(8px)" : "none",
              }}
            >
              {word}{" "}
            </motion.span>
          );
        })}
      </motion.div>
    );
  };

  return (
    <div className={cn("font-bold leading-tight", className)}>
      {renderWords()}
    </div>
  );
};
