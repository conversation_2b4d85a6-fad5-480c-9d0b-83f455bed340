import { useState, useEffect } from 'react';
import { getList } from '../lib/pocketbase';

interface Specialist {
  id: string;
  name: string;
  position: string;
  photo: string;
  experience: string;
  achievements: string;
}

export default function SpecialistsList() {
  const [specialists, setSpecialists] = useState<Specialist[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSpecialists() {
      try {
        setLoading(true);
        // Получаем список специалистов из коллекции 'specialists'
        const response = await getList('specialists');
        setSpecialists(response.items);
        setError(null);
      } catch (err) {
        console.error('Ошибка при загрузке специалистов:', err);
        setError('Не удалось загрузить список специалистов');
      } finally {
        setLoading(false);
      }
    }

    fetchSpecialists();
  }, []);

  if (loading) {
    return <div className="text-center p-8">Загрузка специалистов...</div>;
  }

  if (error) {
    return <div className="text-center p-8 text-red-500">{error}</div>;
  }

  if (specialists.length === 0) {
    return <div className="text-center p-8">Специалисты не найдены</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
      {specialists.map((specialist) => (
        <div
          key={specialist.id}
          className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
        >
          {specialist.photo && (
            <div className="aspect-w-3 aspect-h-2">
              <img
                src={`${import.meta.env.PUBLIC_API_URL || 'https://pb.stom-line.ru'}/api/files/specialists/${specialist.id}/${specialist.photo}`}
                alt={specialist.name}
                className="w-full h-full object-cover"
              />
            </div>
          )}
          <div className="p-4">
            <h3 className="text-xl font-semibold text-gray-800">{specialist.name}</h3>
            <p className="text-green-600 font-medium">{specialist.position}</p>
            {specialist.experience && (
              <p className="mt-2 text-gray-600">
                <span className="font-medium">Опыт работы:</span> {specialist.experience}
              </p>
            )}
            {specialist.achievements && (
              <div className="mt-3">
                <h4 className="font-medium text-gray-700">Достижения:</h4>
                <div
                  className="mt-1 text-gray-600 text-sm"
                  dangerouslySetInnerHTML={{ __html: specialist.achievements }}
                />
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
