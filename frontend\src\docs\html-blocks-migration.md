# Миграция статического контента в PocketBase

## Обзор

Этот документ описывает процесс переноса статического текстового контента из компонента HomeSection в PocketBase для возможности редактирования без пересборки проекта.

## Что было сделано

### 1. Расширена схема PocketBase

Коллекция `html_blocks` была расширена следующими полями:

- `key` (string, required) - уникальный ключ блока для идентификации
- `title` (string, optional) - название блока для удобства редактирования
- `description` (string, optional) - описание назначения блока
- `content` (editor, optional) - содержимое блока
- `section` (select, required) - секция сайта (hero, features, about, contact, general)
- `type` (select, required) - тип контента (badge, heading, subheading, paragraph, button, phone, email, address, html)
- `is_active` (boolean) - активность блока
- `sort_order` (number) - порядок сортировки

### 2. Обновлены TypeScript типы

Интерфейс `HtmlBlocks` в `frontend/src/lib/pocketbase-types.ts` обновлен в соответствии с новой схемой.

### 3. Добавлены API функции

В `frontend/src/lib/api.ts` добавлены функции:

- `getHtmlBlocks(section?: string)` - получение всех HTML блоков или блоков конкретной секции
- `getHtmlBlockByKey(key: string)` - получение блока по ключу
- `getHtmlBlocksForSection(section: string)` - получение блоков секции, сгруппированных по типам

### 4. Модифицированы компоненты

#### HeroSection
- Добавлен пропс `htmlBlocks` для получения данных из PocketBase
- Статический контент заменен на динамический через функцию `getBlockContent()`
- Сохранены все стили и структура компонента

#### HomeSection
- Добавлен пропс `htmlBlocks`
- Добавлена фильтрация блоков по секциям
- Передача соответствующих блоков в подкомпоненты

#### Главная страница (index.astro)
- Добавлено получение HTML блоков из PocketBase
- Передача блоков в компонент HomeSection

## Структура данных

### Блоки для секции Hero

| Ключ | Тип | Описание |
|------|-----|----------|
| `hero_badge` | badge | Бейдж "Сайт в разработке" |
| `hero_title` | heading | Основной заголовок "Стоматология" |
| `hero_title_accent` | heading | Акцентная часть "Будущего" |
| `hero_description` | paragraph | Описание под заголовком |
| `hero_button_primary` | button | Основная кнопка "Связаться с нами" |
| `hero_button_secondary` | button | Вторичная кнопка "Узнать больше" |
| `hero_phone` | phone | Номер телефона |
| `hero_development_text` | paragraph | Текст в анимированном блоке |

## Импорт данных

### 1. Файл данных

Создан файл `frontend/src/data/html-blocks-import.json` с начальными данными для всех блоков.

### 2. Скрипт импорта

Создан скрипт `frontend/src/scripts/import-html-blocks.js` для автоматического импорта данных в PocketBase.

#### Использование:

```bash
cd frontend
bun import-html-blocks.js
```

#### Переменные окружения:

```bash
POCKETBASE_URL=https://pb.stom-line.ru
POCKETBASE_ADMIN_EMAIL=<EMAIL>
POCKETBASE_ADMIN_PASSWORD=your-password
```

**Примечание:** Скрипт обновлен для совместимости с PocketBase v0.23.0+, где администраторы авторизуются через коллекцию `_superusers` вместо устаревшего API `admins`.

## Преимущества новой системы

1. **Редактирование без пересборки** - контент можно изменять через админ-панель PocketBase
2. **Структурированность** - контент организован по секциям и типам
3. **Типизация** - полная поддержка TypeScript
4. **Совместимость** - интеграция с существующей системой администрирования
5. **Fallback** - если данные не загружены, используется статический контент

## Дальнейшие шаги

1. Запустить скрипт импорта для заполнения коллекции html_blocks
2. Протестировать отображение контента на главной странице
3. При необходимости расширить систему на другие секции (FeaturesSection, AboutSection, ContactSection)
4. Добавить поддержку HTML блоков в систему администрирования контента

## Совместимость

Система полностью совместима с:
- Существующей системой администрирования контента
- Корпоративными цветами (#8BC34A, #4E8C29, #85C026)
- Текущей архитектурой проекта
- SEO оптимизацией
