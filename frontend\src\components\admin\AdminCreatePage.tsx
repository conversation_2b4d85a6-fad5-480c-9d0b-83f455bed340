import * as React from 'react';
import { AdminAuth } from './AdminAuth';
import { UniversalRecordEditor } from './UniversalRecordEditor';

interface AdminCreatePageProps {
  collection: string;
  collectionSchema: any;
  pbUrl: string;
}

export const AdminCreatePage: React.FC<AdminCreatePageProps> = ({
  collection,
  collectionSchema,
  pbUrl,
}) => {
  const [isAuthenticated, setIsAuthenticated] = React.useState<boolean>(false);
  const [token, setToken] = React.useState<string>('');
  const [loading, setLoading] = React.useState<boolean>(true);
  const [createdRecordId, setCreatedRecordId] = React.useState<string | null>(null);

  React.useEffect(() => {
    const savedToken = localStorage.getItem('pb_token');
    console.log('Проверка токена:', savedToken ? 'найден' : 'не найден');

    if (savedToken) {
      setToken(savedToken);
      setIsAuthenticated(true);
    }
    setLoading(false);
  }, []);

  const handleAuth = (authToken: string) => {
    console.log('Авторизация успешна');
    setToken(authToken);
    setIsAuthenticated(true);
    localStorage.setItem('pb_token', authToken);

    // Устанавливаем cookie для middleware
    document.cookie = `pb_token=${authToken}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
    console.log('Токен синхронизирован с cookies для middleware');
  };

  const handleLogout = () => {
    setToken('');
    setIsAuthenticated(false);
    localStorage.removeItem('pb_token');

    // Удаляем cookie
    document.cookie = 'pb_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    console.log('Токен удален из localStorage и cookies');
  };

  const handleRecordCreated = (record: any) => {
    console.log('Запись создана:', record);
    setCreatedRecordId(record.id);
    
    // Перенаправляем на страницу редактирования созданной записи
    setTimeout(() => {
      window.location.href = `/admin/edit/${collection}/${record.id}`;
    }, 1500);
  };

  // Показываем загрузку пока проверяем токен
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Проверка авторизации...</p>
        </div>
      </div>
    );
  }

  // Проверяем авторизацию
  if (!isAuthenticated) {
    return (
      <div>
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
          <p className="text-blue-700 text-sm">
            Для создания записей необходима авторизация
          </p>
        </div>
        <AdminAuth onAuth={handleAuth} pbUrl={pbUrl} />
      </div>
    );
  }

  // Показываем сообщение об успешном создании
  if (createdRecordId) {
    return (
      <div className="text-center py-12">
        <div className="mb-4">
          <svg className="w-16 h-16 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Запись успешно создана!
        </h3>
        <p className="text-gray-600 mb-4">
          ID: <span className="font-mono text-sm">{createdRecordId}</span>
        </p>
        <p className="text-sm text-gray-500">
          Перенаправление на страницу редактирования...
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6 pb-4 border-b">
        <div>
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-2"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Назад
          </button>
        </div>
        <button
          onClick={handleLogout}
          className="text-sm text-red-600 hover:text-red-800"
        >
          Выйти
        </button>
      </div>

      <UniversalRecordEditor
        collection={collection}
        recordId="new"
        mode="inline"
        pbUrl={pbUrl}
        token={token || 'test-token'}
        onSave={handleRecordCreated}
        isCreateMode={true}
      />
    </div>
  );
};
