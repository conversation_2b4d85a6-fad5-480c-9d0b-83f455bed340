@import 'tailwindcss';
@import "tw-animate-css";

@config "../../tailwind.config.ts";

@custom-variant dark (&:is(.dark *));
@custom-variant accessibility (&:is(.accessibility *));

#page_wall_posts {
  color: red!important;
}

:root {
  --background: oklch(1.00 0 0);
  --foreground: oklch(0.37 0.03 259.73);
  --card: oklch(1.00 0 0);
  --card-foreground: oklch(0.37 0.03 259.73);
  --popover: oklch(1.00 0 0);
  --popover-foreground: oklch(0.37 0.03 259.73);
  --primary: oklch(0.72 0.19 149.58);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.85 0.12 85.05);
  --secondary-foreground: oklch(0.45 0.03 256.80);
  --muted: oklch(0.97 0.00 264.54);
  --muted-foreground: oklch(0.55 0.02 264.36);
  --accent: oklch(0.95 0.05 163.05);
  --accent-foreground: oklch(0.37 0.03 259.73);
  --destructive: oklch(0.64 0.21 25.33);
  --destructive-foreground: oklch(1.00 0 0);
  --border: oklch(0.93 0.01 264.53);
  --input: oklch(0.93 0.01 264.53);
  --ring: oklch(0.72 0.19 149.58);
  --chart-1: oklch(0.72 0.19 149.58);
  --chart-2: oklch(0.70 0.15 162.48);
  --chart-3: oklch(0.60 0.13 163.23);
  --chart-4: oklch(0.51 0.10 165.61);
  --chart-5: oklch(0.43 0.09 166.91);
  --sidebar: oklch(0.95 0.03 236.82);
  --sidebar-foreground: oklch(0.37 0.03 259.73);
  --sidebar-primary: oklch(0.72 0.19 149.58);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.95 0.05 163.05);
  --sidebar-accent-foreground: oklch(0.37 0.03 259.73);
  --sidebar-border: oklch(0.93 0.01 264.53);
  --sidebar-ring: oklch(0.72 0.19 149.58);

  /* Дополнительные цвета для улучшенной контрастности */
  --text-high-contrast: #2D5016; /* Очень темно-зеленый для высокого контраста */

  --font-sans: DM Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
}

.dark {
  --background: oklch(0.21 0.04 265.75);
  --foreground: oklch(0.87 0.01 258.34);
  --card: oklch(0.28 0.04 260.03);
  --card-foreground: oklch(0.87 0.01 258.34);
  --popover: oklch(0.28 0.04 260.03);
  --popover-foreground: oklch(0.87 0.01 258.34);
  --primary: oklch(0.77 0.15 163.22);
  --primary-foreground: oklch(0.21 0.04 265.75);
  --secondary: oklch(0.34 0.03 260.91);
  --secondary-foreground: oklch(0.71 0.01 286.07);
  --muted: oklch(0.28 0.04 260.03);
  --muted-foreground: oklch(0.55 0.02 264.36);
  --accent: oklch(0.37 0.03 259.73);
  --accent-foreground: oklch(0.71 0.01 286.07);
  --destructive: oklch(0.64 0.21 25.33);
  --destructive-foreground: oklch(0.21 0.04 265.75);
  --border: oklch(0.45 0.03 256.80);
  --input: oklch(0.45 0.03 256.80);
  --ring: oklch(0.77 0.15 163.22);
  --chart-1: oklch(0.77 0.15 163.22);
  --chart-2: oklch(0.78 0.13 181.91);
  --chart-3: oklch(0.72 0.19 149.58);
  --chart-4: oklch(0.70 0.15 162.48);
  --chart-5: oklch(0.60 0.13 163.23);
  --sidebar: oklch(0.28 0.04 260.03);
  --sidebar-foreground: oklch(0.87 0.01 258.34);
  --sidebar-primary: oklch(0.77 0.15 163.22);
  --sidebar-primary-foreground: oklch(0.21 0.04 265.75);
  --sidebar-accent: oklch(0.37 0.03 259.73);
  --sidebar-accent-foreground: oklch(0.71 0.01 286.07);
  --sidebar-border: oklch(0.45 0.03 256.80);
  --sidebar-ring: oklch(0.77 0.15 163.22);
  --font-sans: DM Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
}

/* Версия для слабовидящих - базовые переменные */
.accessibility {
  --background: #ffffff;
  --foreground: #000000;
  --card: #ffffff;
  --card-foreground: #000000;
  --popover: #ffffff;
  --popover-foreground: #000000;
  --primary: #000000;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5;
  --secondary-foreground: #000000;
  --muted: #f0f0f0;
  --muted-foreground: #000000;
  --accent: #e5e5e5;
  --accent-foreground: #000000;
  --destructive: #000000;
  --destructive-foreground: #ffffff;
  --border: #000000;
  --input: #ffffff;
  --ring: #000000;
}

/* Основные стили для версии слабовидящих */
.accessibility {
  background-color: #ffffff !important;
  color: #000000 !important;
  font-size: 18px !important;
  line-height: 1.6 !important;
}

/* Убираем анимации и эффекты */
.accessibility *,
.accessibility *::before,
.accessibility *::after {
  animation: none !important;
  transition: none !important;
  transform: none !important;
  box-shadow: none !important;
  background-image: none !important;
}

/* Увеличиваем размер текста */
.accessibility {
  font-size: 18px !important;
}

.accessibility h1 {
  font-size: 32px !important;
  font-weight: bold !important;
  color: #000000 !important;
}

.accessibility h2 {
  font-size: 28px !important;
  font-weight: bold !important;
  color: #000000 !important;
}

.accessibility h3 {
  font-size: 24px !important;
  font-weight: bold !important;
  color: #000000 !important;
}

.accessibility h4,
.accessibility h5,
.accessibility h6 {
  font-size: 20px !important;
  font-weight: bold !important;
  color: #000000 !important;
}

.accessibility p,
.accessibility span,
.accessibility div,
.accessibility a,
.accessibility li {
  font-size: 18px !important;
  color: #000000 !important;
  background-color: transparent !important;
}

/* Кнопки в режиме доступности */
.accessibility button {
  min-height: 48px !important;
  padding: 12px 16px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 2px solid #000000 !important;
  border-radius: 4px !important;
}

.accessibility button:hover {
  background-color: #000000 !important;
  color: #ffffff !important;
}

/* Ссылки в режиме доступности */
.accessibility a {
  color: #000000 !important;
  text-decoration: underline !important;
  font-size: 18px !important;
}

.accessibility a:hover {
  background-color: #000000 !important;
  color: #ffffff !important;
  padding: 2px 4px !important;
}

/* Поля ввода */
.accessibility input,
.accessibility textarea,
.accessibility select {
  min-height: 48px !important;
  padding: 12px !important;
  font-size: 18px !important;
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 2px solid #000000 !important;
}

/* Четкие фокусы */
.accessibility *:focus {
  outline: 3px solid #000000 !important;
  outline-offset: 2px !important;
}

/* Убираем декоративные элементы в версии для слабовидящих */
.accessibility img[alt=""] {
  display: none !important;
}

/* Упрощаем главную страницу */
.accessibility .bg-gradient-to-b,
.accessibility .bg-gradient-to-r,
.accessibility .bg-gradient-to-br {
  background: #ffffff !important;
}

/* Убираем фоновые эффекты */
.accessibility .blur-\[100px\],
.accessibility .blur-\[80px\],
.accessibility .blur-\[120px\] {
  display: none !important;
}

/* Убираем сетку */
.accessibility .bg-\[url\(\'\/grid\.svg\'\)\] {
  background-image: none !important;
}

/* Упрощаем карточки */
.accessibility .rounded-2xl,
.accessibility .rounded-xl,
.accessibility .rounded-lg {
  border-radius: 8px !important;
  border: 2px solid #000000 !important;
  background-color: #ffffff !important;
}

/* Упрощаем кнопки */
.accessibility .bg-olive-600,
.accessibility .bg-olive-500,
.accessibility .bg-blue-600,
.accessibility .bg-green-600 {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 2px solid #000000 !important;
}

/* Убираем canvas и анимации */
.accessibility canvas {
  display: none !important;
}

.accessibility .animate-spin-slow,
.accessibility .animate-spin-slow-reverse {
  animation: none !important;
}

/* Упрощаем hero секцию */
.accessibility .aspect-square {
  display: none !important;
}

/* Упрощаем текстовые эффекты */
.accessibility .bg-clip-text,
.accessibility .text-transparent {
  background: none !important;
  color: #000000 !important;
  -webkit-background-clip: unset !important;
  background-clip: unset !important;
}

/* Стили для страниц услуг и специалистов */
.accessibility .shadow-md,
.accessibility .shadow-lg,
.accessibility .shadow-xl {
  box-shadow: none !important;
  border: 2px solid #000000 !important;
}

/* Упрощаем карточки специалистов */
.accessibility .group {
  border: 2px solid #000000 !important;
  background-color: #ffffff !important;
}

.accessibility .group:hover {
  background-color: #f5f5f5 !important;
}

/* Упрощаем изображения */
.accessibility img {
  border: 2px solid #000000 !important;
  border-radius: 8px !important;
}

/* Убираем hover эффекты */
.accessibility .hover\:scale-105,
.accessibility .hover\:scale-110 {
  transform: none !important;
}

.accessibility .transition-transform,
.accessibility .transition-all {
  transition: none !important;
}

/* Упрощаем бейджи и теги */
.accessibility .bg-olive-100,
.accessibility .bg-olive-200,
.accessibility .bg-blue-100,
.accessibility .bg-green-100 {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 1px solid #000000 !important;
}

/* Упрощаем цветные элементы */
.accessibility .text-olive-600,
.accessibility .text-olive-700,
.accessibility .text-olive-800,
.accessibility .text-blue-600,
.accessibility .text-green-600 {
  color: #000000 !important;
}

/* Упрощаем фоны секций */
.accessibility .bg-olive-50,
.accessibility .bg-olive-100,
.accessibility .bg-blue-50,
.accessibility .bg-green-50 {
  background-color: #ffffff !important;
}

/* Стили для форм и поиска */
.accessibility .border-olive-200,
.accessibility .border-olive-300,
.accessibility .border-blue-200,
.accessibility .border-green-200 {
  border-color: #000000 !important;
}

.accessibility .focus\:border-olive-400,
.accessibility .focus\:border-blue-400,
.accessibility .focus\:ring-olive-400,
.accessibility .focus\:ring-blue-400 {
  border-color: #000000 !important;
  box-shadow: 0 0 0 2px #000000 !important;
}

/* Упрощаем аккордеоны и раскрывающиеся элементы */
.accessibility .accordion-trigger,
.accessibility .collapsible-trigger {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 2px solid #000000 !important;
}

/* Стили для навигации и меню */
.accessibility .sticky,
.accessibility .fixed {
  position: static !important;
}

/* Упрощаем индикаторы и прогресс-бары */
.accessibility .bg-current,
.accessibility .bg-primary {
  background-color: #000000 !important;
}

/* Убираем backdrop-blur эффекты */
.accessibility .backdrop-blur-sm,
.accessibility .backdrop-blur {
  backdrop-filter: none !important;
}

/* Упрощаем модальные окна */
.accessibility .modal,
.accessibility .dialog {
  background-color: #ffffff !important;
  border: 3px solid #000000 !important;
}

/* Стили для таблиц */
.accessibility table {
  border-collapse: collapse !important;
  border: 2px solid #000000 !important;
}

.accessibility th,
.accessibility td {
  border: 1px solid #000000 !important;
  padding: 12px !important;
  background-color: #ffffff !important;
  color: #000000 !important;
}

.accessibility th {
  background-color: #f5f5f5 !important;
  font-weight: bold !important;
}

/* Дополнительные размеры шрифта */
.font-large {
  font-size: 20px !important;
}

.font-large h1 { font-size: 36px !important; }
.font-large h2 { font-size: 32px !important; }
.font-large h3 { font-size: 28px !important; }
.font-large h4, .font-large h5, .font-large h6 { font-size: 24px !important; }
.font-large p, .font-large span, .font-large div, .font-large a, .font-large li { font-size: 20px !important; }

.font-extra-large {
  font-size: 24px !important;
}

.font-extra-large h1 { font-size: 40px !important; }
.font-extra-large h2 { font-size: 36px !important; }
.font-extra-large h3 { font-size: 32px !important; }
.font-extra-large h4, .font-extra-large h5, .font-extra-large h6 { font-size: 28px !important; }
.font-extra-large p, .font-extra-large span, .font-extra-large div, .font-extra-large a, .font-extra-large li { font-size: 24px !important; }

/* Высокий контраст */
.contrast-high {
  background-color: #ffffff !important;
  color: #000000 !important;
}

.contrast-high * {
  background-color: #ffffff !important;
  color: #000000 !important;
}

.contrast-high button {
  background-color: #ffffff !important;
  color: #000000 !important;
  border: 2px solid #000000 !important;
}

/* Инвертированные цвета */
.contrast-inverted {
  background-color: #000000 !important;
  color: #ffffff !important;
}

.contrast-inverted * {
  background-color: #000000 !important;
  color: #ffffff !important;
}

.contrast-inverted button {
  background-color: #000000 !important;
  color: #ffffff !important;
  border: 2px solid #ffffff !important;
}

.contrast-inverted button:hover {
  background-color: #ffffff !important;
  color: #000000 !important;
}

/* Утилиты для доступности */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Утилитарные классы для высокого контраста */
.text-high-contrast {
  color: var(--text-high-contrast) !important;
}

.text-accessible-green {
  color: #2D5016 !important;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Улучшенная навигация с клавиатуры */
*:focus-visible {
  outline: 3px solid #000;
  outline-offset: 2px;
  transition: outline-color 0.2s, outline-width 0.2s, outline-offset 0.2s;
}

.accessibility *:focus-visible {
  outline: 4px solid #000 !important;
  outline-offset: 3px !important;
  transition: outline-color 0.2s, outline-width 0.2s, outline-offset 0.2s !important;
}

/* Убираем outline для мыши, оставляем для клавиатуры */
*:focus:not(:focus-visible) {
  outline: none;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
  --animate-shine: shine var(--duration) infinite linear;
  @keyframes shine {
  0% {
    background-position: 0% 0%;
    }
  50% {
    background-position: 100% 100%;
    }
  to {
    background-position: 0% 0%;
    }
  }
}

@keyframes sway {
  0% {
    transform: rotate(-5deg);
  }
  100% {
    transform: rotate(5deg);
  }
}

/* ...existing code... */
.accessibility .accessibility-toggle--fixed {
  position: fixed !important;
  top: 16px !important;
  right: 16px !important;
  z-index: 9999 !important;
  background: none !important;
  box-shadow: none !important;
}

.accessibility a:hover,
.accessibility .group:hover,
.accessibility .hover\:text-\[\#8BC34A\]:hover {
  background-color: #fff !important;
  color: #000 !important;
  text-decoration: underline !important;
  border-color: #000 !important;
}

.accessibility button:hover,
.accessibility .btn:hover {
  background-color: #fff !important;
  color: #000 !important;
  border-color: #000 !important;
}

.accessibility .accessibility-toggle--fixed button:hover {
  background-color: #fff !important;
  color: #000 !important;
  border-color: #000 !important;
}

.accessibility.accessibility-underline-links a {
  text-decoration: underline !important;
}

.accessibility.accessibility-hide-images img {
  display: none !important;
}

.accessibility:not(.accessibility-animations) *,
.accessibility:not(.accessibility-animations) *::before,
.accessibility:not(.accessibility-animations) *::after {
  animation: none !important;
  transition: none !important;
}

/* Custom animations for achievements section */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes pulse-number {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes gradient-bg {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 1s ease-in-out infinite;
}

.animate-pulse-number {
  animation: pulse-number 2s ease-in-out infinite;
}

.animate-gradient-bg {
  animation: gradient-bg 3s ease infinite;
  background-size: 300% 300%;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

.animation-delay-100 {
  animation-delay: 0.1s;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-300 {
  animation-delay: 0.3s;
}

.bg-300\% {
  background-size: 300% 300%;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Утилиты для обрезки текста */
@layer utilities {
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}