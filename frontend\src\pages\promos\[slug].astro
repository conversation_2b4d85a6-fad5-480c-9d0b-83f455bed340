---
import Layout from '../../layouts/Layout.astro';
import PocketBase from 'pocketbase';
import { Button } from '../../components/ui/button';
import { CalendarIcon, PhoneIcon, MapPinIcon, ClockIcon } from 'lucide-react';
import { generateArticleJsonLd } from '../../lib/seo-config';
import RDFaMarkup from '../../components/RDFaMarkup.astro';

// URL API
const PUBLIC_API_URL = 'https://pb.stom-line.ru';

export async function getStaticPaths() {
  const pb = new PocketBase(PUBLIC_API_URL);

  try {
    const promos = await pb.collection('promos').getFullList({
      expand: 'related_services',
    });

    return promos.map((promo) => ({
      params: { slug: promo.slug || promo.id },
      props: { promo },
    }));
  } catch (error) {
    console.error('Ошибка при получении данных об акциях:', error);
    return [];
  }
}

const { promo } = Astro.props;

// Проверяем, что promo определен
if (!promo) {
  return Astro.redirect('/promos');
}

// Получаем URL изображения, если есть
let imageUrl = '';
if (promo?.image) {
  try {
    imageUrl = `${PUBLIC_API_URL}/api/files/${promo.collectionId || 'pbc_promos'}/${promo.id}/${promo.image}`;
  } catch (error) {
    console.error('Ошибка при получении URL изображения:', error);
  }
}

// Получаем связанные услуги
const relatedServices = promo?.expand?.related_services || [];

// Форматируем даты
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('ru-RU', { day: 'numeric', month: 'long', year: 'numeric' });
  } catch (error) {
    console.error('Ошибка при форматировании даты:', error);
    return '';
  }
};

const startDate = formatDate(promo?.start_date);
const endDate = formatDate(promo?.end_date);

// Проверяем, активна ли акция
const isActive = promo?.is_active || false;

// SEO данные
const promoTitle = promo?.title || 'Акция';
const title = promo?.meta_title || `${promoTitle} | Акции STOM-LINE в Мурманске`;
const description = promo?.meta_description || `${promoTitle} - специальное предложение стоматологической клиники STOM-LINE в Мурманске. ${promo?.subtitle || 'Выгодные условия и скидки на стоматологические услуги.'}`;
const image = imageUrl || 'https://stom-line.ru/og-image.jpg';

// Генерируем структурированные данные для акции
const promoJsonLd = generateArticleJsonLd(promo, 'promo');

// Формируем ключевые слова
const keywords = [
  'акции стоматология',
  'скидки на лечение зубов',
  'STOM-LINE',
  'стоматология Мурманск',
  'специальные предложения',
  promoTitle
].filter(Boolean);
---

<Layout
  title={title}
  description={description}
  keywords={keywords}
  image={image}
  type="article"
  jsonLd={promoJsonLd}
  article={{
    publishedTime: promo.start_date || promo.created,
    modifiedTime: promo.updated,
    author: 'STOM-LINE',
    section: 'Акции'
  }}
>
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-5xl mx-auto">
      <!-- Хлебные крошки -->
      <div class="text-sm text-olive-500 mb-6">
        <a href="/" class="hover:text-olive-700">Главная</a>
        <span class="mx-2">/</span>
        <a href="/promos" class="hover:text-olive-700">Акции</a>
        <span class="mx-2">/</span>
        <span class="text-olive-700">{promo.title}</span>
        <a href={`/admin/edit/promos/${promo.id}`} target="_blank" rel="noopener noreferrer" class="ml-4 text-xs text-blue-600 underline">Редактировать</a>
      </div>

      <!-- Основная информация об акции -->
      <div class="bg-white rounded-xl shadow-md overflow-hidden mb-10">
        {imageUrl && (
          <div class="w-full h-64 md:h-80">
            <img
              src={imageUrl}
              alt={promo.title}
              class="w-full h-full object-cover"
            />
          </div>
        )}

        <div class="p-8">
          <div class="flex flex-wrap items-center gap-3 mb-4">
            {isActive ? (
              <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                Активная акция
              </span>
            ) : (
              <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                Акция завершена
              </span>
            )}

            {(startDate || endDate) && (
              <span class="flex items-center gap-1 text-olive-600 text-sm">
                <ClockIcon className="h-4 w-4" />
                {startDate && endDate ? `${startDate} - ${endDate}` : startDate || `До ${endDate}`}
              </span>
            )}
          </div>

          <h1 class="text-3xl font-bold text-olive-800 mb-3">{promo.title}</h1>

          {promo.subtitle && (
            <p class="text-xl text-olive-600 mb-6">{promo.subtitle}</p>
          )}

          <div class="flex flex-col sm:flex-row gap-4 mt-6">
            <Button className="bg-olive-600 hover:bg-olive-700 text-white flex items-center gap-2">
              <PhoneIcon className="h-4 w-4" />
              <a href="tel:+78152525708" class="inline-block">Записаться на прием</a>
            </Button>

            <Button variant="outline" className="border-olive-600 text-olive-700 hover:bg-olive-50 flex items-center gap-2">
              <a href="/promos" class="inline-block">Все акции</a>
            </Button>
          </div>
        </div>
      </div>

      <!-- Подробное описание акции -->
      {promo.content && (
        <div class="bg-white rounded-xl shadow-md overflow-hidden p-8 mb-10">
          <h2 class="text-2xl font-semibold text-olive-800 mb-4">Описание акции</h2>
          <div class="prose prose-olive max-w-none" set:html={promo.content} />
        </div>
      )}

      <!-- Связанные услуги -->
      {relatedServices.length > 0 && (
        <div class="bg-white rounded-xl shadow-md overflow-hidden p-8 mb-10">
          <h2 class="text-2xl font-semibold text-olive-800 mb-4">Связанные услуги</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {relatedServices.map((service) => (
              <a
                href={`/services/${service.slug || service.id}`}
                class="p-4 border border-olive-100 rounded-lg hover:bg-olive-50 transition-colors"
              >
                <h3 class="font-medium text-olive-800">{service.name}</h3>
                {service.short_description && (
                  <p class="mt-1 text-sm text-olive-600 line-clamp-2">{service.short_description}</p>
                )}
              </a>
            ))}
          </div>
        </div>
      )}

      <!-- Условия акции -->
      <div class="bg-olive-50 rounded-xl shadow-sm overflow-hidden p-8 mb-10">
        <h2 class="text-2xl font-semibold text-olive-800 mb-4">Условия акции</h2>

        <ul class="space-y-3 text-olive-700">
          <li class="flex items-start gap-2">
            <span class="text-olive-500 font-bold">•</span>
            <span>Акция действует при условии полной оплаты услуг</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="text-olive-500 font-bold">•</span>
            <span>Скидки по акции не суммируются с другими скидками и специальными предложениями</span>
          </li>
          <li class="flex items-start gap-2">
            <span class="text-olive-500 font-bold">•</span>
            <span>Администрация клиники оставляет за собой право изменить условия акции</span>
          </li>
          {startDate && endDate && (
            <li class="flex items-start gap-2">
              <span class="text-olive-500 font-bold">•</span>
              <span>Срок действия акции: с {startDate} по {endDate}</span>
            </li>
          )}
        </ul>
      </div>

      <!-- Контактная информация -->
      <div class="bg-white rounded-xl shadow-md overflow-hidden p-8">
        <h2 class="text-2xl font-semibold text-olive-800 mb-4">Контактная информация</h2>

        <div class="flex flex-col gap-4">
          <div class="flex items-start gap-3">
            <PhoneIcon className="h-5 w-5 text-olive-600 mt-0.5" />
            <div>
              <p class="font-medium text-olive-800">Телефон для записи</p>
              <a href="tel:+78152525708" class="text-olive-600 hover:text-olive-800">+7 (8152) 52-57-08</a>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <MapPinIcon className="h-5 w-5 text-olive-600 mt-0.5" />
            <div>
              <p class="font-medium text-olive-800">Адрес клиники</p>
              <p class="text-olive-600">г. Мурманск, ул. Полярные Зори, д. 35/2</p>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <CalendarIcon className="h-5 w-5 text-olive-600 mt-0.5" />
            <div>
              <p class="font-medium text-olive-800">Часы работы</p>
              <p class="text-olive-600">Пн-Пт: 9:00-20:00, Сб: 9:00-18:00, Вс: выходной</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- RDFa разметка для акции -->
  <RDFaMarkup type="article" data={{...promo, type: 'promo'}} />
</Layout>
