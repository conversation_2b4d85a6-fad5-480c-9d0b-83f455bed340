#!/usr/bin/env node

/**
 * Скрипт для инициализаци<PERSON>earch
 * 
 * Этот скрипт выполняет полную инициализацию MeiliSearch:
 * 1. Настраивает индексы
 * 2. Синхронизирует данные из PocketBase
 * 3. Создает общий индекс для поиска по всем коллекциям
 * 
 * Использование:
 * node init-meilisearch.js
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

// Загружаем переменные окружения из .env файла, если он есть
try {
  const envPath = path.resolve(__dirname, '../.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    
    for (const line of envVars) {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    }
  }
} catch (error) {
  console.error('Ошибка при загрузке .env файла:', error);
}

// Определяем URL для запроса
const baseUrl = process.env.FRONTEND_URL || 'http://frontend:4321';
const apiUrl = `${baseUrl}/api/sync-search`;
const apiKey = process.env.API_KEY || 'stomline-sync-key';

// Функция для выполнения HTTP запроса
function makeRequest(url, method, data, headers) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    const req = client.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({ statusCode: res.statusCode, data: parsedData });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Функция для проверки доступности сервера
async function waitForServer(maxAttempts = 30, interval = 5000) {
  console.log(`Ожидаем доступности сервера ${baseUrl}...`);
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const healthUrl = `${baseUrl}/api/health`;
      console.log(`Попытка ${attempt}/${maxAttempts}: проверка ${healthUrl}`);
      
      const response = await makeRequest(healthUrl, 'GET');
      
      if (response.statusCode === 200) {
        console.log('Сервер доступен!');
        return true;
      }
      
      console.log(`Сервер недоступен (статус ${response.statusCode}), ожидаем...`);
    } catch (error) {
      console.log(`Сервер недоступен (${error.message}), ожидаем...`);
    }
    
    // Ждем перед следующей попыткой
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  throw new Error(`Сервер не стал доступен после ${maxAttempts} попыток`);
}

// Основная функция
async function main() {
  try {
    console.log('Запуск инициализации MeiliSearch...');
    
    // Ждем доступности сервера
    await waitForServer();
    
    // Выполняем запрос к API для инициализации
    console.log(`Отправляем запрос на инициализацию MeiliSearch: ${apiUrl}`);
    const response = await makeRequest(apiUrl, 'POST', { action: 'init' }, {
      'x-api-key': apiKey
    });
    
    if (response.statusCode === 200 && response.data.success) {
      console.log('Инициализация MeiliSearch успешно завершена!');
      console.log(response.data.message);
      process.exit(0);
    } else {
      console.error('Ошибка при инициализации MeiliSearch:');
      console.error(response.data);
      process.exit(1);
    }
  } catch (error) {
    console.error('Ошибка при выполнении скрипта:', error);
    process.exit(1);
  }
}

// Запускаем скрипт
main();
