# Project Structure

## Root Level
- **Configuration files**: `astro.config.mjs`, `tailwind.config.ts`, `tsconfig.json`, `vitest.config.ts`
- **Package management**: `package.json`, `bun.lockb`, `package-lock.json`
- **Docker**: `Dockerfile`, `.dockerignore`
- **Code quality**: `.prettierrc`, `.gitignore`

## Source Structure (`src/`)

### Pages (`src/pages/`)
- **Public pages**: `index.astro`, `about.astro`, `services.astro`, `specialists.astro`, `news.astro`, `reviews.astro`, `faq.astro`, `contacts.astro`
- **Dynamic routes**: `[slug].astro` patterns for services, specialists, news, promos
- **Admin panel**: `src/pages/admin/` - Complete admin interface with authentication
- **API endpoints**: `src/pages/api/` - Server-side API routes

### Components (`src/components/`)
- **UI components**: `src/components/ui/` - Reusable Radix UI components
- **Admin components**: `src/components/admin/` - Admin panel specific components
- **Page components**: Component files matching page names (e.g., `services.tsx`, `news-page.tsx`)
- **Accessibility**: `src/components/accessibility/` - Accessibility features
- **Magic UI**: `src/components/magicui/` - Enhanced UI components

### Libraries (`src/lib/`)
- **PocketBase integration**: `pocketbase.ts`, `pocketbase-admin.ts`, `pocketbase-types.ts`
- **Search**: `meilisearch.ts`, `sync-meilisearch.ts`
- **Utilities**: `utils.ts`, `constants.ts`, `api.ts`
- **SEO**: `seo-config.ts`, `seo-validator.ts`

### Other Directories
- **Middleware**: `src/middleware/` - Authentication and request handling
- **Styles**: `src/styles/global.css` - Global styles
- **Tests**: `src/tests/` - Test files with setup
- **Scripts**: `src/scripts/` - Build and migration scripts
- **Data**: `src/data/` - Static data imports

## Key Patterns

### File Naming
- **Astro pages**: `.astro` extension for server-rendered pages
- **React components**: `.tsx` for interactive components
- **Utilities**: `.ts` for TypeScript modules
- **Kebab-case**: Used for most file names (`news-page.tsx`, `contact-section.tsx`)

### Component Organization
- **Page-specific components**: Named after their corresponding pages
- **Reusable UI**: Stored in `ui/` subdirectory
- **Feature-specific**: Grouped in subdirectories (admin, accessibility, examples)

### Admin Panel Structure
- **Authentication**: Login, token management, auth sync
- **CRUD operations**: Create, edit, delete for all collections
- **Rich text editing**: TiptapEditor integration
- **Universal editor**: Dynamic form generation for any collection

### Import Aliases
- `@/*` maps to `./src/*` for clean imports
- React JSX configured for React 19

## Static Assets
- **Public directory**: Static files served directly
- **Images**: Logos, icons, placeholder images
- **Configuration**: `robots.txt`, `site.webmanifest`, `browserconfig.xml`