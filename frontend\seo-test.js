// Скрипт для тестирования SEO оптимизации
import { seoConfig, getPageSEO, generateOrganizationJsonLd, generateServiceJsonLd, generateDoctorJsonLd } from './src/lib/seo-config.js';
import { SEOValidator } from './src/lib/seo-validator.js';

console.log('🔍 Запуск SEO тестирования для STOM-LINE...\n');

// Тестовые данные
const testPages = {
  'Главная страница': getPageSEO('home'),
  'Услуги': getPageSEO('services'),
  'Специалисты': getPageSEO('specialists'),
  'Цены': getPageSEO('prices'),
  'Отзывы': getPageSEO('reviews'),
  'Новости': getPageSEO('news'),
  'Акции': getPageSEO('promos'),
  'FAQ': getPageSEO('faq'),
  'О клинике': getPageSEO('about')
};

// Добавляем JSON-LD данные
testPages['Главная страница'].jsonLd = generateOrganizationJsonLd();

// Тестовые данные для услуги
const testService = {
  name: 'Лечение кариеса',
  slug: 'lechenie-kariesa',
  short_description: 'Современное лечение кариеса без боли',
  expand: {
    category: { name: 'Терапевтическая стоматология' }
  }
};

testPages['Тестовая услуга'] = {
  title: 'Лечение кариеса | Стоматологические услуги STOM-LINE в Мурманске',
  description: 'Лечение кариеса в стоматологической клинике STOM-LINE. Современное лечение кариеса без боли. Записаться на прием: +7 (8152) 52-57-08',
  keywords: ['лечение кариеса', 'стоматологические услуги', 'стоматология Мурманск', 'STOM-LINE'],
  image: 'https://stom-line.ru/og-image.jpg',
  jsonLd: generateServiceJsonLd(testService)
};

// Тестовые данные для врача
const testDoctor = {
  name: 'Иван',
  surname: 'Иванов',
  patronymic: 'Иванович',
  slug: 'ivanov-ivan',
  position: 'Врач-стоматолог терапевт',
  short_description: 'Опытный врач-стоматолог с 10-летним стажем',
  expand: {
    specializations: [
      { name: 'Терапевтическая стоматология' },
      { name: 'Эндодонтия' }
    ]
  }
};

testPages['Тестовый врач'] = {
  title: 'Иванов Иван Иванович - Врач-стоматолог терапевт | Стоматология STOM-LINE в Мурманске',
  description: 'Врач-стоматолог терапевт Иванов Иван Иванович в стоматологической клинике STOM-LINE. Опытный врач-стоматолог с 10-летним стажем. Записаться на прием: +7 (8152) 52-57-08',
  keywords: ['Иванов Иван Иванович', 'Врач-стоматолог терапевт', 'стоматолог Мурманск', 'врач стоматолог', 'стоматология STOM-LINE'],
  image: 'https://stom-line.ru/og-image.jpg',
  jsonLd: generateDoctorJsonLd(testDoctor)
};

// Запуск валидации
console.log('📊 Результаты SEO валидации:\n');

const report = SEOValidator.generateSEOReport(testPages);
console.log(report);

// Дополнительные проверки
console.log('\n🔧 Дополнительные проверки:\n');

// Проверка конфигурации
console.log('✅ SEO конфигурация загружена');
console.log(`📍 Сайт: ${seoConfig.site.name} (${seoConfig.site.url})`);
console.log(`🏢 Организация: ${seoConfig.organization.name}`);
console.log(`📞 Телефон: ${seoConfig.organization.telephone}`);
console.log(`📧 Email: ${seoConfig.organization.email}`);

// Проверка структурированных данных
console.log('\n📋 Проверка структурированных данных:');

try {
  const orgJsonLd = generateOrganizationJsonLd();
  console.log('✅ JSON-LD для организации сгенерирован');
  console.log(`   Тип: ${orgJsonLd['@type']}`);
  console.log(`   Название: ${orgJsonLd.name}`);
  
  const serviceJsonLd = generateServiceJsonLd(testService);
  console.log('✅ JSON-LD для услуги сгенерирован');
  console.log(`   Тип: ${serviceJsonLd['@type']}`);
  console.log(`   Название: ${serviceJsonLd.name}`);
  
  const doctorJsonLd = generateDoctorJsonLd(testDoctor);
  console.log('✅ JSON-LD для врача сгенерирован');
  console.log(`   Тип: ${doctorJsonLd['@type']}`);
  console.log(`   Имя: ${doctorJsonLd.name}`);
  
} catch (error) {
  console.log('❌ Ошибка при генерации JSON-LD:', error.message);
}

// Проверка URL и изображений
console.log('\n🖼️ Проверка ресурсов:');
console.log(`✅ Базовый URL: ${seoConfig.site.url}`);
console.log(`✅ Логотип: ${seoConfig.organization.logo}`);
console.log(`✅ OG изображение: ${seoConfig.defaults.image}`);

// Проверка социальных сетей
console.log('\n📱 Социальные сети:');
Object.entries(seoConfig.social).forEach(([platform, url]) => {
  if (url) {
    console.log(`✅ ${platform}: ${url}`);
  }
});

// Проверка ключевых слов
console.log('\n🔑 Ключевые слова:');
console.log(`📝 Основные: ${seoConfig.site.keywords.slice(0, 5).join(', ')}...`);
console.log(`📊 Всего: ${seoConfig.site.keywords.length}`);

// Проверка страниц в конфигурации
console.log('\n📄 Настроенные страницы:');
Object.keys(seoConfig.pages).forEach(pageKey => {
  console.log(`✅ ${pageKey}`);
});

console.log('\n🎉 SEO тестирование завершено!');
console.log('\n💡 Рекомендации:');
console.log('1. Убедитесь, что все изображения og-image существуют');
console.log('2. Проверьте корректность URL в production');
console.log('3. Добавьте реальные коды верификации для поисковых систем');
console.log('4. Настройте мониторинг позиций в поисковых системах');
console.log('5. Регулярно обновляйте sitemap.xml');
console.log('6. Мониторьте Core Web Vitals в Google Search Console');
