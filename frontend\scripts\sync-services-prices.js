#!/usr/bin/env node

/**
 * Скрипт для синхронизации только услуг и цен с MeiliSearch
 */

const PocketBase = require('pocketbase/cjs');
const { MeiliSearch } = require('meilisearch');

// Индексы для MeiliSearch
const INDEXES = {
  SERVICES: 'services',
  PRICES: 'prices'
};

async function main() {
  try {
    // Получаем URL PocketBase и MeiliSearch из переменных окружения или используем значения по умолчанию
    const pocketbaseUrl = process.env.PUBLIC_API_URL || 'https://pb.stom-line.ru';
    const searchUrl = process.env.PUBLIC_SEARCH_URL || 'https://search.stom-line.ru';
    const searchApiKey = process.env.PUBLIC_SEARCH_API_KEY || 'Nc040stomline';
    
    console.log(`Подключение к PocketBase: ${pocketbaseUrl}`);
    console.log(`Подключение к MeiliSearch: ${searchUrl}`);
    
    // Создаем экземпляры PocketBase и MeiliSearch
    const pb = new PocketBase(pocketbaseUrl);
    const searchClient = new MeiliSearch({
      host: searchUrl,
      apiKey: searchApiKey
    });
    
    // Синхронизация услуг
    console.log('\n=== Синхронизация услуг ===');
    await syncServices(pb, searchClient);
    
    // Синхронизация цен
    console.log('\n=== Синхронизация цен ===');
    await syncPrices(pb, searchClient);
    
    console.log('\nСинхронизация завершена.');
  } catch (error) {
    console.error('Ошибка при выполнении скрипта:', error);
    process.exit(1);
  }
}

/**
 * Синхронизация услуг
 */
async function syncServices(pb, searchClient) {
  try {
    console.log('Получение услуг из PocketBase...');
    
    // Получаем все услуги из PocketBase без фильтров
    const services = await pb.collection('services').getFullList();
    console.log(`Получено ${services.length} услуг из PocketBase`);
    
    if (services.length === 0) {
      console.warn('Нет услуг для синхронизации!');
      return false;
    }
    
    // Выводим первую услугу для проверки
    console.log('Пример услуги:', JSON.stringify(services[0], null, 2));
    
    // Преобразуем данные для MeiliSearch
    const servicesForMeiliSearch = services.map(service => {
      return {
        id: service.id,
        name: service.name || '',
        slug: service.slug || service.id,
        category: service.category || '',
        category_id: service.category || '',
        short_description: service.short_description || '',
        content: service.content || '',
        meta_title: service.meta_title || '',
        meta_description: service.meta_description || '',
        is_featured: service.is_featured || false,
        _index: INDEXES.SERVICES
      };
    });
    
    console.log(`Подготовлено ${servicesForMeiliSearch.length} услуг для MeiliSearch`);
    
    // Отправляем данные в MeiliSearch
    const index = searchClient.index(INDEXES.SERVICES);
    await index.addDocuments(servicesForMeiliSearch);
    
    console.log(`Синхронизировано ${servicesForMeiliSearch.length} услуг`);
    return true;
  } catch (error) {
    console.error('Ошибка при синхронизации услуг:', error);
    console.error('Детали ошибки:', error.message, error.stack);
    return false;
  }
}

/**
 * Синхронизация цен
 */
async function syncPrices(pb, searchClient) {
  try {
    console.log('Получение цен из PocketBase...');
    
    // Получаем все цены из PocketBase без фильтров
    const prices = await pb.collection('prices').getFullList();
    console.log(`Получено ${prices.length} цен из PocketBase`);
    
    if (prices.length === 0) {
      console.warn('Нет цен для синхронизации!');
      return false;
    }
    
    // Выводим первую цену для проверки
    console.log('Пример цены:', JSON.stringify(prices[0], null, 2));
    
    // Преобразуем данные для MeiliSearch
    const pricesForMeiliSearch = prices.map(price => {
      return {
        id: price.id,
        name: price.name || 'Услуга',
        price: price.price || 0,
        old_price: price.old_price || 0,
        description: price.description || '',
        service_id: price.service || '',
        category_id: price.category || '',
        is_featured: price.is_featured || false,
        _index: INDEXES.PRICES
      };
    });
    
    console.log(`Подготовлено ${pricesForMeiliSearch.length} цен для MeiliSearch`);
    
    // Отправляем данные в MeiliSearch
    const index = searchClient.index(INDEXES.PRICES);
    await index.addDocuments(pricesForMeiliSearch);
    
    console.log(`Синхронизировано ${pricesForMeiliSearch.length} цен`);
    return true;
  } catch (error) {
    console.error('Ошибка при синхронизации цен:', error);
    console.error('Детали ошибки:', error.message, error.stack);
    return false;
  }
}

// Запускаем скрипт
main();
