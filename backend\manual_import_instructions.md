# Инструкция по ручному импорту коллекции callback_requests

## 📋 Пошаговая инструкция

### Шаг 1: Авторизация в админ-панели

1. Откройте https://pb.stom-line.ru/_/
2. Войдите с учетными данными:
   - Email: `<EMAIL>`
   - Пароль: `!10Havafi1`

### Шаг 2: Импорт коллекции

1. В левом меню нажмите **"Collections"**
2. Нажмите кнопку **"Import collections"** (справа вверху)
3. Выберите файл `callback_requests_manual_import.json`
4. Нажмите **"Import"**

### Шаг 3: Проверка коллекции

После импорта вы должны увидеть новую коллекцию **"callback_requests"** в списке.

### Шаг 4: Настройка правил доступа (если нужно)

1. Откройте коллекцию **"callback_requests"**
2. Перейдите на вкладку **"API Rules"**
3. Убедитесь, что правила настроены следующим образом:

#### List/Search rule:
```
@request.auth.id != ""
```

#### View rule:
```
@request.auth.id != ""
```

#### Create rule:
```
(оставить пустым для публичного доступа)
```

#### Update rule:
```
@request.auth.id != ""
```

#### Delete rule:
```
@request.auth.id != ""
```

### Шаг 5: Добавление тестовых данных (опционально)

1. Откройте коллекцию **"callback_requests"**
2. Нажмите **"New record"**
3. Заполните поля:
   - **name**: "Тестовый пользователь"
   - **phone**: "+7 (999) 123-45-67"
   - **message**: "Тестовая заявка"
   - **status**: "new"
   - **source**: "test"
4. Нажмите **"Create"**

### Шаг 6: Проверка API

После создания коллекции API callback должен автоматически начать работать с PocketBase.

Проверить можно:
1. Открыв форму обратного звонка на сайте
2. Заполнив и отправив форму
3. Проверив, что заявка появилась в админ-панели PocketBase

## 🔧 Структура полей коллекции

| Поле | Тип | Обязательное | Описание |
|------|-----|--------------|----------|
| **name** | text | ✅ | Имя клиента (2-100 символов) |
| **phone** | text | ✅ | Номер телефона (10-20 символов) |
| **message** | text | ❌ | Сообщение (до 1000 символов) |
| **status** | select | ✅ | Статус: new, in_progress, completed, cancelled |
| **source** | text | ❌ | Источник заявки (до 100 символов) |
| **ip_address** | text | ❌ | IP-адрес клиента (до 45 символов) |
| **user_agent** | text | ❌ | User-Agent браузера (до 500 символов) |
| **processed_at** | date | ❌ | Дата обработки |
| **processed_by** | text | ❌ | Кто обработал (до 100 символов) |
| **notes** | text | ❌ | Заметки (до 2000 символов) |

## 🎯 Что происходит после импорта

1. **API обновлен**: Файл `frontend/src/pages/api/callback.ts` уже обновлен для работы с PocketBase
2. **Типы добавлены**: В `frontend/src/lib/pocketbase-types.ts` добавлен интерфейс `CallbackRequests`
3. **Схема обновлена**: В `frontend/src/lib/pocketbase-schema.ts` добавлена поддержка новой коллекции

## 🚀 Тестирование

После импорта коллекции:

1. Откройте сайт
2. Найдите форму "Заказать звонок" (в шапке или на главной странице)
3. Заполните форму:
   - Имя: "Тест"
   - Телефон: "+7 999 123 45 67"
   - Сообщение: "Тестовая заявка"
4. Отправьте форму
5. Проверьте в админ-панели PocketBase, что заявка появилась

## 🔍 Мониторинг заявок

В админ-панели PocketBase вы можете:

- **Просматривать все заявки** в коллекции callback_requests
- **Фильтровать по статусу**: новые, в обработке, завершенные
- **Сортировать по дате** создания
- **Редактировать заявки**: изменять статус, добавлять заметки
- **Экспортировать данные** в различных форматах

## 📊 Полезные фильтры

В админ-панели можете использовать фильтры:

### Новые заявки:
```
status = "new"
```

### Заявки за сегодня:
```
created >= "2024-01-15 00:00:00"
```

### Заявки с определенного источника:
```
source = "homepage-form"
```

## ⚠️ Важные замечания

1. **Резервное копирование**: Регулярно создавайте резервные копии данных
2. **Мониторинг**: Следите за новыми заявками и своевременно их обрабатывайте
3. **Безопасность**: Не изменяйте правила доступа без необходимости
4. **Производительность**: При большом количестве заявок используйте фильтры и пагинацию

## 🆘 Устранение неполадок

### Заявки не сохраняются
- Проверьте правила доступа (Create rule должен быть пустым)
- Проверьте логи в консоли браузера
- Убедитесь, что PocketBase доступен

### Ошибки валидации
- Проверьте соответствие данных требованиям полей
- Убедитесь, что обязательные поля заполнены

### Проблемы с доступом
- Проверьте авторизацию в админ-панели
- Убедитесь в корректности правил доступа
