#!/bin/bash

# Скрипт для проверки настройки PocketBase

echo "🔍 Проверяем настройку PocketBase..."

# Проверяем наличие необходимых файлов
echo "📁 Проверяем файлы..."

files_to_check=(
    "Dockerfile"
    "docker-compose.yml"
    "docker-compose.prod.yml"
    "deploy.sh"
    "backup-data.sh"
    "restore-data.sh"
    ".env.example"
    "hooks/sync-meilisearch.js"
    "hooks/callback-notifications.js"
    "test-notifications.js"
    "NOTIFICATIONS_SETUP.md"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ] || [ -d "$file" ]; then
        echo "✅ $file - найден"
    else
        echo "❌ $file - не найден"
    fi
done

echo ""
echo "📋 Содержимое папки hooks:"
ls -la hooks/

echo ""
echo "🐳 Проверяем Dockerfile..."
if grep -q "COPY hooks/ /pb/pb_hooks/" Dockerfile; then
    echo "✅ Хуки копируются в образ"
else
    echo "❌ Хуки не копируются в образ"
fi

if grep -q "VOLUME.*pb_data" Dockerfile; then
    echo "✅ Volume для данных настроен"
else
    echo "❌ Volume для данных не настроен"
fi

echo ""
echo "📦 Проверяем .dockerignore..."
if grep -q "pb_data/" .dockerignore; then
    echo "✅ Данные исключены из образа (не будут перезатираться)"
else
    echo "❌ Данные не исключены из образа"
fi

echo ""
echo "🎯 Настройка завершена!"
echo ""
echo "📝 Следующие шаги:"
echo "1. Скопируйте .env.example в .env и настройте переменные"
echo "2. Для локальной разработки: docker-compose up --build"
echo "3. Для деплоя в облако: ./deploy.sh v1.0.0"
echo "4. Для бэкапа данных: ./backup-data.sh"
echo ""
echo "📖 Подробные инструкции в DEPLOY.md"
