/**
 * Утилиты для работы с авторизацией в middleware
 */

/**
 * Проверяет, авторизован ли пользователь через Astro.locals
 * @param locals Astro.locals объект
 * @returns boolean
 */
export function isUserAuthenticated(locals: App.Locals): boolean {
  return locals.isAuthenticated === true;
}

/**
 * Получает информацию о текущем пользователе из Astro.locals
 * @param locals Astro.locals объект
 * @returns user object или undefined
 */
export function getCurrentUser(locals: App.Locals): App.Locals['user'] {
  return locals.user;
}

/**
 * Проверяет, имеет ли пользователь права администратора
 * @param locals Astro.locals объект
 * @returns boolean
 */
export function isAdmin(locals: App.Locals): boolean {
  // Проверяем, что пользователь авторизован И имеет тип 'admin'
  return locals.isAuthenticated === true && locals.user?.type === 'admin';
}

/**
 * Проверяет, является ли пользователь суперпользователем (высший уровень доступа)
 * @param locals Astro.locals объект
 * @returns boolean
 */
export function isSuperUser(locals: App.Locals): boolean {
  return isAdmin(locals); // В текущей реализации admin = superuser
}

/**
 * Проверяет, может ли пользователь редактировать контент
 * @param locals Astro.locals объект
 * @returns boolean
 */
export function canEditContent(locals: App.Locals): boolean {
  return isAdmin(locals);
}