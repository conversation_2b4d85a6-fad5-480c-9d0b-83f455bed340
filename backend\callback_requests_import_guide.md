# Импорт коллекции "Запросы на обратный звонок" в PocketBase

## Описание коллекции

Коллекция `callback_requests` предназначена для хранения заявок на обратный звонок от пользователей сайта.

## Поля коллекции

### Основные поля
- **name** (text, обязательное) - Имя клиента (2-100 символов)
- **phone** (text, обязательное) - Номер телефона с валидацией (10-20 символов)
- **message** (text, опциональное) - Дополнительное сообщение от клиента (до 1000 символов)

### Поля для управления
- **status** (select, обязательное) - Статус заявки:
  - `new` - Новая заявка
  - `in_progress` - В обработке
  - `completed` - Завершена
  - `cancelled` - Отменена

### Технические поля
- **source** (text, опциональное) - Источник заявки (страница сайта, форма и т.д.)
- **ip_address** (text, опциональное) - IP-адрес клиента
- **user_agent** (text, опциональное) - User-Agent браузера клиента

### Поля для обработки
- **processed_at** (date, опциональное) - Дата и время обработки заявки
- **processed_by** (text, опциональное) - Кто обработал заявку
- **notes** (text, опциональное) - Заметки по заявке (до 2000 символов)

## Правила доступа

- **Создание (createRule)**: `""` - Любой может создать заявку (публичный доступ)
- **Просмотр (listRule/viewRule)**: `@request.auth.id != ""` - Только авторизованные пользователи
- **Обновление (updateRule)**: `@request.auth.id != ""` - Только авторизованные пользователи
- **Удаление (deleteRule)**: `@request.auth.id != ""` - Только авторизованные пользователи

## Индексы

Созданы индексы для оптимизации запросов:
- По статусу заявки
- По дате создания
- По номеру телефона

## Инструкция по импорту

### Способ 1: Через веб-интерфейс PocketBase

1. Откройте админ-панель PocketBase
2. Перейдите в раздел "Collections"
3. Нажмите "Import collections"
4. Загрузите файл `callback_requests_collection.json`
5. Подтвердите импорт

### Способ 2: Через API

```bash
# Импорт через API (требует авторизации администратора)
curl -X POST "http://localhost:8090/api/collections/import" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d @callback_requests_collection.json
```

## Примеры данных

### Создание новой заявки

```json
{
  "name": "Иван Петров",
  "phone": "+7 (999) 123-45-67",
  "message": "Хочу записаться на консультацию к стоматологу",
  "status": "new",
  "source": "contact-form-header",
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
}
```

### Обновление статуса заявки

```json
{
  "status": "completed",
  "processed_at": "2024-01-15T14:30:00Z",
  "processed_by": "Администратор",
  "notes": "Клиент записан на прием 20.01.2024 в 15:00"
}
```

## Интеграция с фронтендом

После импорта коллекции обновите файл `frontend/src/pages/api/callback.ts`:

1. Замените функцию `saveCallbackRequest` на реальное сохранение в PocketBase
2. Добавьте обработку ошибок и валидацию
3. Обновите типы в `frontend/src/lib/pocketbase-types.ts`

### Пример интеграции

```typescript
import pb from '@/lib/pocketbase';

const saveCallbackRequest = async (data: CallbackFormData): Promise<{ success: boolean; id?: string; error?: string }> => {
  try {
    const record = await pb.collection('callback_requests').create({
      name: data.name,
      phone: data.phone,
      message: data.message || '',
      status: 'new',
      source: 'website-form',
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      user_agent: request.headers.get('user-agent')
    });

    return {
      success: true,
      id: record.id
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
};
```

## Администрирование

### Просмотр заявок

```javascript
// Получить все новые заявки
const newRequests = await pb.collection('callback_requests').getList(1, 50, {
  filter: 'status = "new"',
  sort: '-created'
});

// Получить заявки за сегодня
const today = new Date().toISOString().split('T')[0];
const todayRequests = await pb.collection('callback_requests').getList(1, 50, {
  filter: `created >= "${today} 00:00:00"`,
  sort: '-created'
});
```

### Обновление статуса

```javascript
// Отметить заявку как обработанную
await pb.collection('callback_requests').update(recordId, {
  status: 'completed',
  processed_at: new Date().toISOString(),
  processed_by: 'Менеджер Иванов',
  notes: 'Клиент записан на прием'
});
```

## Безопасность

- Коллекция настроена для публичного создания записей (для форм на сайте)
- Просмотр и редактирование доступны только авторизованным пользователям
- Рекомендуется настроить rate limiting для предотвращения спама
- Добавьте CAPTCHA для дополнительной защиты от ботов
