import { useEffect, useState } from 'react'
import { Menu, Phone, Search as SearchIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer'
import { Search } from '@/components/search'
import { AccessibilityToggle } from '@/components/accessibility/AccessibilityToggle'

interface NavItem {
  title: string
  href: string
}

export default function Header() {
  // Состояние для отслеживания скролла страницы
  // const [scrolled, setScrolled] = useState(true)

  // Состояние для контроля drawer
  const [drawerOpen, setDrawerOpen] = useState(false)

  const navItems: NavItem[] = [
    { title: 'Главная', href: '/' },
    { title: 'Услуги', href: '/services' },
    { title: 'Цены', href: '/prices' },
    { title: 'Специалисты', href: '/specialists' },
    { title: 'Акции', href: '/promos' },
    { title: 'Новости', href: '/news' },
    { title: 'Отзывы', href: '/reviews' },
    { title: 'FAQ', href: '/faq' },
    { title: 'Документы', href: '/documents' },
    { title: 'О нас', href: '/about' },
    { title: 'Контакты', href: '/contacts' }
  ]

  useEffect(() => {
    // Функционал скролла отключен, так как не используется в текущей версии
    // const handleScroll = () => {
    //   setScrolled(window.scrollY > 20)
    // }
    // window.addEventListener('scroll', handleScroll)
    // return () => window.removeEventListener('scroll', handleScroll)

    // Закрываем drawer при навигации
    const closeDrawerOnNavigation = () => {
      setDrawerOpen(false)
    }

    // Обработчики для различных типов навигации

    // 1. Astro transitions (основной способ навигации в Astro)
    window.addEventListener('astro:before-preparation', closeDrawerOnNavigation)
    window.addEventListener('astro:after-swap', closeDrawerOnNavigation)

    // 2. Swup навигация (если используется)
    window.addEventListener('swup:contentReplaced', closeDrawerOnNavigation)
    window.addEventListener('swup:pageView', closeDrawerOnNavigation)

    // 3. Стандартная навигация браузера
    const origPush = history.pushState
    history.pushState = function (...args) {
      origPush.apply(this, args)
      closeDrawerOnNavigation()
    }
    const origReplace = history.replaceState
    history.replaceState = function (...args) {
      origReplace.apply(this, args)
      closeDrawerOnNavigation()
    }

    // 4. Навигация назад/вперед
    window.addEventListener('popstate', closeDrawerOnNavigation)

    // 5. Изменение URL (fallback)
    let currentUrl = window.location.href
    const checkUrlChange = () => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href
        closeDrawerOnNavigation()
      }
    }
    const urlCheckInterval = setInterval(checkUrlChange, 100)

    return () => {
      // Очистка всех обработчиков
      window.removeEventListener('astro:before-preparation', closeDrawerOnNavigation)
      window.removeEventListener('astro:after-swap', closeDrawerOnNavigation)
      window.removeEventListener('swup:contentReplaced', closeDrawerOnNavigation)
      window.removeEventListener('swup:pageView', closeDrawerOnNavigation)
      window.removeEventListener('popstate', closeDrawerOnNavigation)

      history.pushState = origPush
      history.replaceState = origReplace
      clearInterval(urlCheckInterval)
    }
  }, [])

  return (
    <>
      {/* Пометка о том, что сайт в разработке */}
      <div className='bg-rose-300 text-white text-center font-bold py-1 sm:py-2 text-xs sm:text-sm sticky top-0 z-50'>
        Сайт в разработке. Демонстрационный контент.
      </div>
      <header
        // className={`sticky top-0 z-50 w-full transition-all duration-300 ${
        //   scrolled ? 'bg-olive-500/20 border-olive-200/50 border-b backdrop-blur-md' : 'bg-transparent'
        // }`}
        className='sticky z-40 w-full transition-all duration-500 ease-in-out bg-gradient-to-r from-[#8BC34A]/10 via-white/40 to-[#8BC34A]/10 backdrop-blur-md border-b border-[#8BC34A]/30'
      >
        <div className='flex  items-center justify-between px-3'>
          <a href='/' className='flex items-center gap-1 sm:gap-2 xl:gap-3 2xl:gap-4'>
            <img
              src='/17a70891-5433-419e-99aa-33350107e7c9-removebg-preview.png'
              alt='Stom-Line'
              className='h-14 w-14 object-contain'
            />
            <span className='logo-font bg-gradient-to-r from-[#4E8C29] to-[#8BC34A] bg-clip-text text-transparent text-sm sm:text-lg md:text-xl font-bold'>Stom Line</span>
          </a>

          {/* Навигация для очень больших экранов (2xl+) */}
          <nav className="hidden 2xl:flex 2xl:gap-10">
            {navItems.map((item) => (
              <a
                key={item.title}
                href={item.href}
                className="group relative text-sm font-medium text-[#4E8C29] transition-all duration-300 hover:text-[#8BC34A] hover:scale-105 whitespace-nowrap"
              >
                {item.title}
                <span className="absolute -bottom-1 left-0 h-[2px] w-0 bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] transition-all duration-300 group-hover:w-full shadow-sm" />
              </a>
            ))}
          </nav>

          {/* Навигация для больших экранов (xl-2xl) - два ряда */}
          <nav className="hidden xl:flex 2xl:hidden flex-col xl:gap-2">
            {/* Первый ряд */}
            <div className="flex xl:gap-6 justify-center">
              {navItems.slice(0, 6).map((item) => (
                <a
                  key={item.title}
                  href={item.href}
                  className="group relative text-sm font-medium text-[#4E8C29] transition-all duration-300 hover:text-[#8BC34A] hover:scale-105 whitespace-nowrap"
                >
                  {item.title}
                  <span className="absolute -bottom-1 left-0 h-[2px] w-0 bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] transition-all duration-300 group-hover:w-full shadow-sm" />
                </a>
              ))}
            </div>
            {/* Второй ряд */}
            <div className="flex xl:gap-6 justify-center">
              {navItems.slice(6).map((item) => (
                <a
                  key={item.title}
                  href={item.href}
                  className="group relative text-sm font-medium text-[#4E8C29] transition-all duration-300 hover:text-[#8BC34A] hover:scale-105 whitespace-nowrap"
                >
                  {item.title}
                  <span className="absolute -bottom-1 left-0 h-[2px] w-0 bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] transition-all duration-300 group-hover:w-full shadow-sm" />
                </a>
              ))}
            </div>
          </nav>

          {/* Навигация для средних экранов (lg-xl) - два ряда */}
          <nav className="hidden lg:flex xl:hidden flex-col lg:gap-1">
            {/* Первый ряд */}
            <div className="flex lg:gap-4 justify-center">
              {navItems.slice(0, 5).map((item) => (
                <a
                  key={item.title}
                  href={item.href}
                  className="group relative text-xs font-medium text-[#4E8C29] transition-all duration-300 hover:text-[#8BC34A] hover:scale-105 whitespace-nowrap"
                >
                  {item.title}
                  <span className="absolute -bottom-1 left-0 h-[1px] w-0 bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] transition-all duration-300 group-hover:w-full shadow-sm" />
                </a>
              ))}
            </div>
            {/* Второй ряд */}
            <div className="flex lg:gap-4 justify-center">
              {navItems.slice(5, 9).map((item) => (
                <a
                  key={item.title}
                  href={item.href}
                  className="group relative text-xs font-medium text-[#4E8C29] transition-all duration-300 hover:text-[#8BC34A] hover:scale-105 whitespace-nowrap"
                >
                  {item.title}
                  <span className="absolute -bottom-1 left-0 h-[1px] w-0 bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] transition-all duration-300 group-hover:w-full shadow-sm" />
                </a>
              ))}
            </div>
          </nav>

          {/* Навигация для планшетов (md-lg) */}
          <nav className="hidden md:flex lg:hidden md:gap-4">
            {navItems.slice(0, 6).map((item) => (
              <a
                key={item.title}
                href={item.href}
                className="group relative text-xs font-medium text-[#4E8C29] transition-all duration-300 hover:text-[#8BC34A] hover:scale-105 whitespace-nowrap"
              >
                {item.title}
                <span className="absolute -bottom-1 left-0 h-[1px] w-0 bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] transition-all duration-300 group-hover:w-full shadow-sm" />
              </a>
            ))}
          </nav>

          <div className="flex flex-wrap items-center gap-1 sm:gap-2 xl:gap-3 2xl:gap-4">
            {/* Переключатель версии для слабовидящих */}
            <div className="hidden lg:block">
              <AccessibilityToggle />
            </div>

            {/* Компонент поиска для десктопа */}
            <div className="hidden md:block">
              <Search buttonVariant="ghost" buttonSize="icon" />
            </div>

            {/* Телефон для связи */}
            <div className="hidden sm:block">
              <a
                href="tel:+78152525708"
                className="bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] hover:from-[#4E8C29] hover:to-[#85C026] text-white shadow-lg transition-all duration-300 hover:scale-105 px-4 py-2 rounded-md text-sm font-medium inline-flex items-center whitespace-nowrap"
              >
                <Phone className="mr-2 h-4 w-4" />
                (8152) 52-57-08
              </a>
            </div>

            {/* Мобильное меню */}
            <div className="sm:hidden">
              <Drawer open={drawerOpen} onOpenChange={setDrawerOpen}>
                <DrawerTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="inline-flex items-center justify-center rounded-full bg-gradient-to-br from-[#8BC34A]/20 to-[#85C026]/20 p-2 text-[#4E8C29] backdrop-blur-sm hover:from-[#8BC34A]/30 hover:to-[#85C026]/30 hover:text-[#8BC34A] focus:outline-none shadow-lg transition-all duration-300 hover:scale-105 active:scale-95"
                  >
                    <Menu className="h-5 w-5" />
                  </Button>
                </DrawerTrigger>
                <DrawerContent className="bg-white max-h-[90vh] flex flex-col">
                  <DrawerHeader className="flex-shrink-0 pb-3 px-6">
                    <DrawerTitle className="text-[#4E8C29] text-xl font-semibold">Меню</DrawerTitle>
                  </DrawerHeader>

                  {/* Поиск и доступность в мобильном меню */}
                  <div className="px-6 pb-4 space-y-4 flex-shrink-0">
                    <DrawerClose asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-[#4E8C29] border-[#8BC34A]/30 h-12 min-h-[48px] text-base font-medium"
                        onClick={() => {
                          // После закрытия drawer запускаем событие для открытия поиска
                          setTimeout(() => {
                            window.dispatchEvent(new Event('openSearchDialog'))
                          }, 100);
                        }}
                      >
                        <SearchIcon className="mr-3 h-5 w-5" />
                        Поиск по сайту
                      </Button>
                    </DrawerClose>

                    {/* Переключатель доступности в мобильном меню */}
                    <div className="w-full flex justify-center py-3 border-t border-[#8BC34A]/20">
                      <AccessibilityToggle />
                    </div>
                  </div>

                  {/* Навигация в мобильном меню - с прокруткой */}
                  <div className="flex-1 overflow-y-auto px-6 pb-4">
                    <div className="space-y-2">
                      {navItems.map((item) => (
                        <DrawerClose asChild key={item.title}>
                          <a
                            href={item.href}
                            className="text-[#4E8C29] hover:bg-[#8BC34A]/20 hover:text-[#4E8C29] flex items-center rounded-lg px-4 py-3 text-base font-medium transition-colors min-h-[52px]"
                          >
                            {item.title}
                          </a>
                        </DrawerClose>
                      ))}
                    </div>
                  </div>

                  <DrawerFooter className="flex-shrink-0 pt-4 border-t border-[#8BC34A]/20">
                    <div className="space-y-4 px-6 py-4">
                      <a
                        href="tel:+78152525708"
                        className="bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] hover:from-[#4E8C29] hover:to-[#85C026] text-white shadow-lg transition-all duration-300 hover:scale-105 px-6 py-3 rounded-lg text-base font-medium inline-flex items-center justify-center w-full"
                      >
                        <Phone className="mr-3 h-5 w-5" />
                        (8152) 52-57-08
                      </a>
                    </div>
                  </DrawerFooter>
                </DrawerContent>
              </Drawer>
            </div>
          </div>
        </div>
      </header>
    </>
  )
}
