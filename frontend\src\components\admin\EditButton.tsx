import * as React from 'react';
import { Edit } from 'lucide-react';
import { UniversalRecordEditor } from './UniversalRecordEditor';

interface EditButtonProps {
  collection: string;
  id: string;
  position?: 'top-right' | 'bottom-right' | 'inline';
  size?: 'sm' | 'md';
  variant?: 'icon' | 'text';
  className?: string;
  isAuthenticated?: boolean;
  // Новые опции для UniversalRecordEditor
  useModal?: boolean; // Использовать модальное окно вместо перехода на страницу
  showQuickFields?: boolean; // Показывать только основные поля
  allowedFields?: string[]; // Ограничить редактирование определенными полями
  initialRecord?: Record<string, any>; // Начальные данные записи
}

export const EditButton: React.FC<EditButtonProps> = ({
  collection,
  id,
  position = 'top-right',
  size = 'sm',
  variant = 'icon',
  className = '',
  isAuthenticated,
  useModal = false,
  showQuickFields = false,
  allowedFields,
  initialRecord,
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const [isEditorOpen, setIsEditorOpen] = React.useState(false);

  React.useEffect(() => {
    // Если передан prop isAuthenticated, используем его
    if (isAuthenticated !== undefined) {
      setIsVisible(isAuthenticated);
      return;
    }

    // Иначе проверяем localStorage (для обратной совместимости)
    const hasAdminToken = localStorage.getItem('pb_token') !== null;
    setIsVisible(hasAdminToken);
  }, [isAuthenticated]);

  if (!isVisible) {
    return null;
  }

  const baseClasses = 'inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors duration-200';
  
  const sizeClasses = {
    sm: 'text-xs p-1',
    md: 'text-sm p-2'
  };

  const positionClasses = {
    'top-right': 'absolute top-2 right-2 z-10',
    'bottom-right': 'absolute bottom-2 right-2 z-10',
    'inline': 'relative'
  };

  const iconSize = size === 'sm' ? 12 : 16;

  const handleClick = () => {
    if (useModal) {
      setIsEditorOpen(true);
    } else {
      const url = `/admin/edit/${collection}/${id}`;
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  const handleClose = () => {
    console.log('handle close!');
    
    setIsEditorOpen(false);
  };

  const handleSave = (record: Record<string, any>) => {
    console.log('Запись обновлена:', record);
    setIsEditorOpen(false);
    // Перезагружаем страницу для отображения изменений
    window.location.reload();
  };

  return (
    <>
      <button
        onClick={handleClick}
        className={`${baseClasses} ${sizeClasses[size]} ${positionClasses[position]} ${className}`}
        title={`Редактировать ${collection} (ID: ${id})`}
      >
        <Edit size={iconSize} />
        {variant === 'text' && (
          <span className="whitespace-nowrap">
            {size === 'sm' ? 'Ред.' : 'Редактировать'}
          </span>
        )}
      </button>

      {useModal && isEditorOpen && (
        <UniversalRecordEditor
          collection={collection}
          recordId={id}
          initialRecord={initialRecord}
          mode="modal"
          showQuickFields={showQuickFields}
          allowedFields={allowedFields}
          isOpen={isEditorOpen}
          onClose={handleClose}
          onSave={handleSave}
          onCancel={handleClose}
        />
      )}
    </>
  );
};
