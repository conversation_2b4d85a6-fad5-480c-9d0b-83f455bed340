#!/bin/bash

# Скрипт для восстановления данных PocketBase из бэкапа

set -e

if [ -z "$1" ]; then
    echo "❌ Укажите файл бэкапа для восстановления"
    echo "Использование: $0 <путь_к_бэкапу.tar.gz> [имя_контейнера]"
    echo ""
    echo "📋 Доступные бэкапы:"
    if [ -d "./backups" ]; then
        ls -lh ./backups/
    else
        echo "Папка backups не найдена"
    fi
    exit 1
fi

BACKUP_FILE=$1
CONTAINER_NAME=${2:-"backend-pocketbase-1"}

if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Файл бэкапа не найден: $BACKUP_FILE"
    exit 1
fi

echo "⚠️  ВНИМАНИЕ: Это действие перезапишет текущие данные!"
echo "📁 Файл бэкапа: $BACKUP_FILE"
echo "📦 Контейнер: $CONTAINER_NAME"
echo ""
read -p "Продолжить? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Восстановление отменено"
    exit 1
fi

echo "🔄 Восстанавливаем данные из бэкапа..."

# Проверяем, запущен ли контейнер
if docker ps | grep -q ${CONTAINER_NAME}; then
    echo "⏹️  Останавливаем контейнер для безопасного восстановления..."
    docker stop ${CONTAINER_NAME}
    CONTAINER_WAS_RUNNING=true
else
    CONTAINER_WAS_RUNNING=false
fi

# Восстанавливаем данные
echo "📦 Извлекаем данные из бэкапа..."
tar xzf "$BACKUP_FILE"

echo "✅ Данные восстановлены из бэкапа"

# Запускаем контейнер обратно, если он был запущен
if [ "$CONTAINER_WAS_RUNNING" = true ]; then
    echo "▶️  Запускаем контейнер обратно..."
    docker start ${CONTAINER_NAME}
    
    # Ждем, пока контейнер запустится
    echo "⏳ Ждем запуска PocketBase..."
    sleep 5
    
    if docker ps | grep -q ${CONTAINER_NAME}; then
        echo "✅ Контейнер успешно запущен"
    else
        echo "❌ Ошибка запуска контейнера"
    fi
fi

echo ""
echo "🎉 Восстановление завершено!"
echo "🌐 PocketBase должен быть доступен на http://localhost:8090"
