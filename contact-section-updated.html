<div class="space-y-6 px-4 sm:px-6 lg:px-8">
  <!-- Заголовок клиники -->
  <div class="text-center">
    <div class="inline-flex items-center rounded-full bg-gradient-to-r from-olive-100/80 to-olive-200/80 px-4 py-2 sm:px-6 sm:py-3 text-base sm:text-lg font-bold text-olive-800 backdrop-blur-sm shadow border border-olive-200/50">
      Клиника на Кольском проспекте
    </div>
  </div>

  <!-- Адрес -->
  <div class="overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-olive-100/80 p-1 backdrop-blur-sm shadow transition-all duration-300">
    <div class="rounded-lg bg-white/60 p-4 sm:p-6">
      <div class="flex items-center mb-3 sm:mb-4"> 
        <div class="bg-olive-100/80 mr-3 flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-full">
          📍
        </div>
        <h3 class="text-olive-800 text-base sm:text-lg font-semibold">Адрес</h3>
      </div>
      <p class="text-olive-800 text-sm sm:text-lg leading-relaxed">
        г. Мурманск, проспект Кольский, д. 202
      </p>
    </div>
  </div>

  <!-- Карта и отзывы -->
  <div class="flex flex-col lg:flex-row gap-4 lg:gap-5 items-start">
    <!-- Карта -->
    <div class="w-full lg:flex-1">
      <div class="overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-olive-100/80 p-1 backdrop-blur-sm shadow transition-all duration-300">
        <div class="rounded-lg bg-white/60 p-3 sm:p-4">
          <div class="flex items-center mb-3 sm:mb-4">
            <div class="bg-olive-100/80 mr-3 flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-full">
              🗺️
            </div>
            <h4 class="text-olive-800 text-sm sm:text-base font-semibold">Расположение</h4>
          </div>
          <div class="relative overflow-hidden rounded-lg shadow-md" style="height: 600px;">
            <a class="absolute top-0 left-0 z-10 text-xs text-gray-300 hover:text-white transition-colors bg-black/20 px-1 rounded-br"
               href="https://yandex.ru/maps/org/stomlayn/1126643368/?utm_medium=mapframe&utm_source=maps">
              Стомлайн
            </a>
            <a class="absolute top-3 left-0 z-10 text-xs text-gray-300 hover:text-white transition-colors bg-black/20 px-1 rounded-br"
               href="https://yandex.ru/maps/23/murmansk/category/dental_clinic/184106132/?utm_medium=mapframe&utm_source=maps">
              Стоматологическая клиника в Мурманске
            </a>
            <iframe
              style="width: 100%; height: 100%;"
              src="https://yandex.ru/map-widget/v1/?ll=33.091158%2C68.901622&mode=poi&poi%5Bpoint%5D=33.089757%2C68.901470&poi%5Buri%5D=ymapsbm1%3A%2F%2Forg%3Foid%3D1126643368&z=15.86"
              frameborder="0"
              allowfullscreen>
            </iframe>
          </div>
        </div>
      </div>
    </div>

    <!-- Отзывы -->
    <div class="w-full lg:flex-1">
      <div class="overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-olive-100/80 p-1 backdrop-blur-sm shadow transition-all duration-300">
        <div class="rounded-lg bg-white/60 p-3 sm:p-4">
          <div class="flex items-center mb-3 sm:mb-4">
            <div class="bg-olive-100/80 mr-3 flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-full">
              ⭐
            </div>
            <h4 class="text-olive-800 text-sm sm:text-base font-semibold">Отзывы пациентов</h4>
          </div>
          <div class="relative overflow-hidden rounded-lg shadow-md" style="height: 600px;">
            <iframe
              style="width: 100%; height: 100%;"
              src="https://yandex.ru/maps-reviews-widget/1126643368?comments">
            </iframe>
            <a class="absolute bottom-2 left-2 right-2 text-center text-xs text-gray-400 hover:text-gray-600 transition-colors bg-white/90 rounded px-2 py-1 backdrop-blur-sm truncate"
               href="https://yandex.ru/maps/org/stomlayn/1126643368/"
               target="_blank"
               rel="noopener">
              Стомлайн на карте Мурманска — Яндекс Карты
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Контактная информация -->
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
    <!-- Телефоны -->
    <div class="overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-olive-100/80 p-1 backdrop-blur-sm shadow transition-all duration-300">
      <div class="rounded-lg bg-white/60 p-4 sm:p-6">
        <div class="flex items-center mb-3 sm:mb-4">
          <div class="bg-olive-100/80 mr-3 flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-full">
            📞
          </div>
          <h3 class="text-olive-800 text-base sm:text-lg font-semibold">Телефоны</h3>
        </div>
        <div class="space-y-3">
          <div class="flex items-center">
            <a class="text-olive-800 text-sm sm:text-lg font-medium hover:text-olive-600 transition-colors break-all" 
               href="tel:+78152525708">
              (8152) <span class="font-mono">52-57-08</span>
            </a>
          </div>
          <div class="flex items-center">
            <a class="text-olive-800 text-sm sm:text-lg font-medium hover:text-olive-600 transition-colors break-all" 
               href="tel:+79537556090">
              8 (953) <span class="font-mono">755-60-90</span>
            </a>
          </div>
          <div class="flex flex-col sm:flex-row sm:items-center gap-2 mt-2">
            <span class="text-olive-600 text-xs sm:text-sm">Доступно в:</span>
            <div class="flex gap-2">
              <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Viber</span>
              <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">WhatsApp</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Время работы -->
    <div class="overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-olive-100/80 p-1 backdrop-blur-sm shadow transition-all duration-300">
      <div class="rounded-lg bg-white/60 p-4 sm:p-6">
        <div class="flex items-center mb-3 sm:mb-4">
          <div class="bg-olive-100/80 mr-3 flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-full">
            🕐
          </div>
          <h3 class="text-olive-800 text-base sm:text-lg font-semibold">Время работы</h3>
        </div>
        <div class="space-y-2">
          <div class="flex justify-between items-center">
            <span class="text-olive-700 font-medium text-sm sm:text-base">пн - пт:</span>
            <span class="text-olive-800 font-mono bg-olive-100/50 px-2 py-1 rounded text-xs sm:text-sm">9:00-20:00</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-olive-700 font-medium text-sm sm:text-base">сб:</span>
            <span class="text-olive-800 font-mono bg-olive-100/50 px-2 py-1 rounded text-xs sm:text-sm">9:00-15:00</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-olive-700 font-medium text-sm sm:text-base">вс:</span>
            <span class="text-red-600 font-medium bg-red-100/50 px-2 py-1 rounded text-xs sm:text-sm">Закрыто</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Как проехать -->
    <div class="overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-olive-100/80 p-1 backdrop-blur-sm shadow transition-all duration-300 sm:col-span-2 lg:col-span-1">
      <div class="rounded-lg bg-white/60 p-4 sm:p-6">
        <div class="flex items-center mb-3 sm:mb-4">
          <div class="bg-olive-100/80 mr-3 flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-full">
            🚌
          </div>
          <h3 class="text-olive-800 text-base sm:text-lg font-semibold">Как проехать</h3>
        </div>
        <div class="space-y-3">
          <div class="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
            <span class="text-olive-700 font-medium text-sm sm:text-base">Остановка:</span>
            <span class="text-olive-800 bg-olive-100/50 px-2 py-1 rounded text-xs sm:text-sm">"Первомайская"</span>
          </div>
          <div>
            <span class="text-olive-700 font-medium block mb-2 text-sm sm:text-base">Автобусы:</span>
            <div class="flex flex-wrap gap-1">
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-mono">5</span>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-mono">10</span>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-mono">19</span>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-mono">27</span>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-mono">106</span>
            </div>
          </div>
          <div>
            <span class="text-olive-700 font-medium block mb-2 text-sm sm:text-base">Троллейбусы:</span>
            <div class="flex flex-wrap gap-1">
              <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-mono">6</span>
              <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-mono">10</span>
            </div>
          </div>
          <div>
            <span class="text-olive-700 font-medium block mb-2 text-sm sm:text-base">Маршрутное такси:</span>
            <div class="flex flex-wrap gap-1">
              <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-mono">10</span>
              <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-mono">51</span>
              <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-mono">53</span>
              <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-mono">100</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
