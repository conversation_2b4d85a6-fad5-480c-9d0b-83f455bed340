/**
 * Утилиты для работы со схемой PocketBase
 */

export interface PBField {
  id: string;
  name: string;
  type: string;
  required?: boolean;
  hidden?: boolean;
  system?: boolean;
  collectionId?: string;
  maxSelect?: number;
  options?: {
    maxSelect?: number;
    collectionId?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface PBCollection {
  id: string;
  name: string;
  type: string;
  fields: PBField[];
  listRule?: string | null;
  viewRule?: string | null;
  createRule?: string | null;
  updateRule?: string | null;
  deleteRule?: string | null;
}

// Кеш для схемы (работает и на сервере, и в браузере)
let schemaCache: PBCollection[] | null = null;
let schemaCacheTime: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 минут

/**
 * Загружает схему PocketBase
 * Работает как на сервере (через fs), так и на клиенте (через fetch)
 */
export async function loadPocketBaseSchema(): Promise<PBCollection[]> {
  // Проверяем кеш с учетом времени
  const now = Date.now();
  if (schemaCache && (now - schemaCacheTime) < CACHE_DURATION) {
    return schemaCache;
  }

  try {
    let collections: PBCollection[];

    if (typeof window !== 'undefined') {
      // Клиентская сторона - используем fetch
      const response = await fetch('/pb_schema.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      collections = await response.json();
    } else {
      // Серверная сторона - используем fs
      const fs = await import('node:fs');
      const path = await import('node:path');

      // В продакшене файл находится в dist/client, в разработке - в public
      let schemaPath = path.join(process.cwd(), 'public', 'pb_schema.json');

      // Проверяем, существует ли файл в public (режим разработки)
      if (!fs.existsSync(schemaPath)) {
        // Если нет, ищем в dist/client (продакшен)
        schemaPath = path.join(process.cwd(), 'dist', 'client', 'pb_schema.json');
      }

      const schemaContent = fs.readFileSync(schemaPath, 'utf-8');
      collections = JSON.parse(schemaContent);
    }

    // Кешируем результат
    schemaCache = collections;
    schemaCacheTime = now;

    return collections;
  } catch (error) {
    console.error('Ошибка загрузки схемы PocketBase:', error);
    throw new Error(`Не удалось загрузить схему PocketBase: ${error}`);
  }
}

/**
 * Получает схему конкретной коллекции
 */
export async function getCollectionSchema(collectionName: string): Promise<PBCollection | null> {
  const collections = await loadPocketBaseSchema();
  return collections.find(c => c.name === collectionName) || null;
}

/**
 * Получает поля коллекции, исключая системные и скрытые
 */
export async function getCollectionFields(
  collectionName: string,
  options: {
    includeSystem?: boolean;
    includeHidden?: boolean;
    onlyRequired?: boolean;
    excludeFields?: string[];
  } = {}
): Promise<PBField[]> {
  const collection = await getCollectionSchema(collectionName);
  if (!collection) {
    throw new Error(`Коллекция ${collectionName} не найдена`);
  }

  let fields = collection.fields;

  // Фильтрация полей
  if (!options.includeSystem) {
    fields = fields.filter(field => !field.system);
  }

  if (!options.includeHidden) {
    fields = fields.filter(field => !field.hidden);
  }

  if (options.onlyRequired) {
    fields = fields.filter(field => field.required);
  }

  // Исключаем стандартные системные поля
  const defaultExcludeFields = ['id', 'created', 'updated'];
  const excludeFields = [...defaultExcludeFields, ...(options.excludeFields || [])];
  fields = fields.filter(field => !excludeFields.includes(field.name));

  if (options.excludeFields && options.excludeFields.length > 0) {
    fields = fields.filter(field => !options.excludeFields?.includes(field.name));
  }

  return fields;
}

/**
 * Проверяет, разрешено ли создание записей в коллекции
 */
export async function isCollectionCreateAllowed(collectionName: string): Promise<boolean> {
  const allowedCollections = [
    'callback_requests', 'doctors', 'services', 'service_categories', 'prices',
    'reviews', 'faq', 'news', 'promos', 'pages',
    'html_blocks', 'personal'
  ];

  return allowedCollections.includes(collectionName);
}

/**
 * Получает список всех доступных для создания коллекций
 */
export async function getCreateableCollections(): Promise<PBCollection[]> {
  const collections = await loadPocketBaseSchema();
  const createableNames = [
    'callback_requests', 'doctors', 'services', 'service_categories', 'prices',
    'reviews', 'faq', 'news', 'promos', 'pages',
    'html_blocks', 'personal'
  ];

  return collections.filter(c => createableNames.includes(c.name));
}

/**
 * Получает отображаемое имя коллекции
 */
export function getCollectionDisplayName(collectionName: string): string {
  const displayNames: Record<string, string> = {
    callback_requests: 'Запросы на обратный звонок',
    doctors: 'Специалисты',
    services: 'Услуги',
    service_categories: 'Категории услуг',
    prices: 'Прайс-лист',
    reviews: 'Отзывы',
    faq: 'FAQ',
    news: 'Новости',
    promos: 'Акции',
    pages: 'Страницы',
    html_blocks: 'HTML-блоки',
    personal: 'Персонал'
  };

  return displayNames[collectionName] || collectionName;
}

/**
 * Получает описание коллекции
 */
export function getCollectionDescription(collectionName: string): string {
  const descriptions: Record<string, string> = {
    callback_requests: 'Заявки на обратный звонок от клиентов',
    doctors: 'Врачи и медицинские специалисты',
    services: 'Медицинские услуги клиники',
    service_categories: 'Категории и группы услуг',
    prices: 'Цены на услуги',
    reviews: 'Отзывы пациентов',
    faq: 'Часто задаваемые вопросы',
    news: 'Новости и статьи',
    promos: 'Акции и специальные предложения',
    pages: 'Статические страницы сайта',
    html_blocks: 'Переиспользуемые HTML-блоки',
    personal: 'Сотрудники клиники'
  };

  return descriptions[collectionName] || `Записи коллекции ${collectionName}`;
}

/**
 * Очищает кеш схемы
 */
export function clearSchemaCache(): void {
  schemaCache = null;
  schemaCacheTime = 0;
}
