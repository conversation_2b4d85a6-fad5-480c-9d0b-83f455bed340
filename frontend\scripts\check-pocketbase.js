#!/usr/bin/env node

/**
 * Скрипт для проверки доступа к PocketBase и получения данных
 */

import PocketBase from 'pocketbase';

async function main() {
  try {
    // Получаем URL PocketBase из переменных окружения или используем значение по умолчанию
    const pocketbaseUrl = process.env.PUBLIC_API_URL || 'https://pb.stom-line.ru';
    console.log(`Подключение к PocketBase: ${pocketbaseUrl}`);
    
    // Создаем экземпляр PocketBase
    const pb = new PocketBase(pocketbaseUrl);
    
    // Проверяем доступ к коллекциям
    console.log('\n=== Проверка доступа к коллекциям ===');
    
    // Проверяем коллекцию doctors
    try {
      const doctors = await pb.collection('doctors').getFullList();
      console.log(`Коллекция doctors: ${doctors.length} записей`);
      if (doctors.length > 0) {
        console.log(`Пример записи: ${JSON.stringify(doctors[0], null, 2)}`);
      }
    } catch (error) {
      console.error('Ошибка при получении doctors:', error);
    }
    
    // Проверяем коллекцию services
    try {
      const services = await pb.collection('services').getFullList();
      console.log(`Коллекция services: ${services.length} записей`);
      if (services.length > 0) {
        console.log(`Пример записи: ${JSON.stringify(services[0], null, 2)}`);
      }
    } catch (error) {
      console.error('Ошибка при получении services:', error);
    }
    
    // Проверяем коллекцию prices
    try {
      const prices = await pb.collection('prices').getFullList();
      console.log(`Коллекция prices: ${prices.length} записей`);
      if (prices.length > 0) {
        console.log(`Пример записи: ${JSON.stringify(prices[0], null, 2)}`);
      }
    } catch (error) {
      console.error('Ошибка при получении prices:', error);
    }
    
    console.log('\nПроверка завершена.');
  } catch (error) {
    console.error('Ошибка при выполнении скрипта:', error);
    process.exit(1);
  }
}

// Запускаем скрипт
main();
