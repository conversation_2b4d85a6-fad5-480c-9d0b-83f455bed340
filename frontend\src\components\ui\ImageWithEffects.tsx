'use client'

import { motion } from 'framer-motion'
import type { ReactNode } from 'react'

interface ImageWithEffectsProps {
  src: string
  alt: string
  className?: string
  imageClassName?: string
  size?: 'mobile' | 'desktop'
  children?: ReactNode
}

export function ImageWithEffects({
  src,
  alt,
  className = '',
  imageClassName = '',
  size = 'desktop',
  children
}: ImageWithEffectsProps) {
  const sizes = {
    mobile: {
      container: 'max-w-[280px]',
      gradientSpots: 'h-20 w-20',
      glowEffects: 'h-16 w-16 sm:h-20 sm:w-20',
      accentPoints: 'h-8 w-8 sm:h-12 sm:w-12',
      particles: 'h-1.5 w-1.5'
    },
    desktop: {
      container: 'max-w-[240px] sm:max-w-[320px] md:max-w-[400px]',
      gradientSpots: 'h-32 w-32',
      glowEffects: 'h-20 w-20 sm:h-28 sm:w-28',
      accentPoints: 'h-12 w-12 sm:h-16 sm:w-16',
      particles: 'h-3 w-3'
    }
  }

  const currentSize = sizes[size]

  return (
    <div className={`relative aspect-square w-full ${currentSize.container} ${className}`}>
      <motion.div
        className='absolute inset-0 flex items-center justify-center overflow-hidden rounded-3xl border-2 border-[#8BC34A]/30 shadow-2xl backdrop-blur-sm'
        initial={{ scale: 0, rotate: size === 'mobile' ? -8 : -5 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ 
          duration: 1, 
          delay: size === 'mobile' ? 0.8 : 0.3, 
          type: 'spring', 
          stiffness: size === 'mobile' ? 120 : 100 
        }}
        style={{
          animation: `float ${size === 'mobile' ? 6 : 4}s ease-in-out infinite, pulse-glow ${size === 'mobile' ? 4 : 3}s ease-in-out infinite`,
          backgroundImage: 'linear-gradient(135deg, rgba(139, 195, 74, 0.1) 0%, rgba(133, 192, 38, 0.15) 50%, rgba(78, 140, 41, 0.1) 100%)',
          opacity: 0.95
        }}
        whileHover={{ 
          scale: size === 'mobile' ? 1.05 : 1.02,
          rotate: size === 'mobile' ? 2 : 0,
          transition: { duration: 0.3 }
        }}
      >
        {/* Основное изображение врачей */}
        <div className='relative h-full w-full overflow-hidden rounded-2xl'>
          <img
            src={src}
            alt={alt}
            className={`h-full w-full object-cover ${imageClassName}`}
            loading='eager'
          />
          
          {/* Градиентный оверлей */}
          <div className='absolute inset-0 bg-gradient-to-t from-[#8BC34A]/40 via-transparent to-transparent'></div>
          
          {/* Светящаяся рамка */}
          <div className='absolute inset-0 rounded-2xl border border-[#8BC34A]/50 shadow-inner'></div>
        </div>

        {/* Декоративные элементы */}
        <div className='pointer-events-none absolute inset-0 overflow-hidden rounded-2xl'>
          {/* Анимированные градиентные пятна */}
          <span className={`pointer-events-none absolute -top-4 -left-4 animate-[gradient-1_8s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#8BC34A]/40 to-transparent blur-2xl ${currentSize.gradientSpots}`}></span>
          <span className={`pointer-events-none absolute -top-4 -right-4 animate-[gradient-2_10s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#85C026]/40 to-transparent blur-2xl ${currentSize.gradientSpots}`}></span>
          <span className={`pointer-events-none absolute -bottom-4 -left-4 animate-[gradient-3_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#4E8C29]/40 to-transparent blur-2xl ${currentSize.gradientSpots}`}></span>
          
          {/* Блестящие частицы */}
          <span className={`pointer-events-none absolute top-6 left-6 animate-pulse rounded-full bg-white/80 blur-sm ${currentSize.particles}`}></span>
          <span 
            className={`pointer-events-none absolute top-12 right-8 animate-pulse rounded-full bg-white/70 blur-sm ${currentSize.particles}`}
            style={{animationDelay: '0.7s'}}
          ></span>
          <span 
            className={`pointer-events-none absolute bottom-8 left-10 animate-pulse rounded-full bg-white/90 blur-sm ${currentSize.particles}`}
            style={{animationDelay: '1.2s'}}
          ></span>
        </div>

        {/* Дополнительный контент */}
        {children}
      </motion.div>

      {/* Улучшенные светящиеся эффекты */}
      <div className={`absolute -bottom-4 -left-4 animate-pulse rounded-full bg-[#8BC34A]/60 blur-3xl ${currentSize.glowEffects}`}></div>
      <div
        className={`absolute -top-4 -right-4 animate-pulse rounded-full bg-[#85C026]/60 blur-3xl ${currentSize.glowEffects}`}
        style={{ animationDelay: '1s' }}
      ></div>
      <div
        className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full bg-[#4E8C29]/30 blur-3xl ${currentSize.glowEffects}`}
        style={{ animationDelay: '0.5s' }}
      ></div>

      {/* Дополнительные акцентные точки */}
      <div
        className={`absolute top-1/4 right-1/4 animate-pulse rounded-full bg-[#8BC34A]/50 blur-2xl ${currentSize.accentPoints}`}
        style={{ animationDelay: '2s' }}
      ></div>
      <div
        className={`absolute bottom-1/4 left-1/4 animate-pulse rounded-full bg-[#85C026]/50 blur-2xl ${currentSize.accentPoints}`}
        style={{ animationDelay: '1.5s' }}
      ></div>
    </div>
  )
}