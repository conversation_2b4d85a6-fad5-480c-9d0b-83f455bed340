'use client'

import { motion } from 'framer-motion'
import { Award } from 'lucide-react'
import { BorderBeam } from './border-beam'
import { CallbackForm } from './callback-form'
import { TextGenerateEffect } from './ui/text-generate-effect'

export default function HeroSection() {
  return (
    <section className='relative mt-10 flex min-h-screen items-center justify-center sm:mt-3'>
      <div className='container mx-auto px-3 sm:px-6'>
        <div className='grid items-center gap-6 lg:grid-cols-2 lg:gap-12'>
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className='order-1 space-y-8 lg:order-1'
          >
            <div className='space-y-6'>
              <div className='text-xl leading-tight font-bold md:text-3xl md:text-5xl'>
                <div className='mb-2'>
                  <TextGenerateEffect words='Хорошая стоматология —' className='text-2xl md:text-5xl text-gray-900' duration={0.8} />
                </div>
                <TextGenerateEffect
                  words='когда бояться нечего'
                  className='text-2xl md:text-5xl bg-gradient-to-r from-[#8BC34A] to-[#7CB342] bg-clip-text text-transparent'
                  duration={0.8}
                />
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className='max-w-lg space-y-3 text-base leading-relaxed sm:text-xl'
              >
                <p className='font-medium text-gray-700'>
                  Мы не предлагаем <span className='font-bold text-[#4E8C29]'>шаблонных решений</span>, а разрабатываем индивидуальный план лечения для каждой
                  улыбки.
                </p>
                <p className='text-gray-700'>Учитываем все ваши особенности, пожелания и бюджет.</p>
                <p className='border-t border-gray-200 pt-2 font-semibold text-gray-800'>Комфортная оплата без скрытых платежей</p>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className='mx-auto hidden max-w-md md:mx-0 lg:block'
            >
              <CallbackForm
                variant='compact'
                title='Оставьте номер — перезвоним за 5 минут'
                className='font-semibold'
              />
            </motion.div>

            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.8, delay: 1 }} className='overflow-scroll scrollbar-hide flex-wrap hidden md:flex items-center gap-6 pt-8'>
              <div className='text-center'>
                <div className='text-base md:text-3xl font-bold text-[#8BC34A]'>15+</div>
                <div className='text-sm font-semibold text-gray-700'>лет опыта</div>
              </div>
              <div className='text-center'>
                <div className='text-base md:text-3xl font-bold text-[#8BC34A]'>5000+</div>
                <div className='text-sm font-semibold text-gray-700'>довольных пациентов</div>
              </div>
              <div className='text-center'>
                <div className='text-base md:text-3xl font-bold text-[#8BC34A]'>5 мин</div>
                <div className='text-sm font-semibold text-gray-700'>обратный звонок</div>
              </div>
              <div className='text-center'>
                <div className='text-base md:text-3xl font-bold text-[#8BC34A]'>0%</div>
                <div className='text-sm font-semibold text-gray-700'>первоначальный взнос</div>
              </div>
            </motion.div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className='relative order-2 flex justify-center lg:order-2'
          >
            <div className='xs:max-w-sm relative mx-auto aspect-[3/4] max-w-[280px] overflow-hidden rounded-2xl border border-white shadow-lg transition-all duration-300 hover:shadow-xl sm:max-w-md md:max-w-lg'>
              <div className='relative h-full w-full overflow-hidden rounded-xl bg-white/60'>
                <img
                  src='/IMG-20250909-WA0008.jpg'
                  alt='Стоматологическая клиника'
                  className='h-full w-full transform object-cover transition-transform duration-700 hover:scale-105'
                />
                <div className='absolute inset-0 bg-gradient-to-t from-[#4E8C29]/80 via-transparent to-transparent' />
                <div className='absolute right-0 bottom-0 left-0 z-20 p-6'>
                  <div className='rounded-lg border border-white/20 bg-[#4D8C29]/10 p-4 text-center text-white backdrop-blur-sm'>
                    <p className='mb-3 text-base font-bold sm:text-lg'>Индивидуальный подход к каждому пациенту</p>
                    <div className='hidden space-y-2 text-sm sm:block'>
                      <p className='leading-relaxed font-medium'>Мы находим, понимаем и учитываем ваши уникальные потребности</p>
                      <p className='leading-relaxed font-medium'>Создаем атмосферу доверия и заботы</p>
                      <p className='leading-relaxed font-medium'>Используем инновационные методы для лучших результатов</p>
                    </div>
                    <div className='mt-3 border-t border-white/20 pt-3'>
                      <p className='text-sm font-semibold text-yellow-200'>Комфортные условия оплаты — разделим стоимость на части!</p>
                    </div>
                  </div>
                  {/* <div className='mt-4 flex-wrap gap-2 justify-center hidden sm:flex'>
                    <span className='bg-gradient-to-r from-[#8BC34A]/20 to-[#85C026]/20 rounded-full px-3 py-1 text-sm font-bold text-white backdrop-blur-md border border-white/20 shadow-lg shadow-black/10'>
                      Индивидуальный подход
                    </span>
                    <span className='bg-yellow-300/30 rounded-full px-3 py-1 text-sm font-medium text-white backdrop-blur-md border border-white/20 shadow-lg shadow-black/10'>
                      Сплит-оплата
                    </span>
                    <span className='bg-orange-500/10 rounded-full px-3 py-1 text-sm font-medium text-white backdrop-blur-md border border-white/20 shadow-lg shadow-black/10'>
                      Уникальные решения
                    </span>
                  </div> */}
                </div>
                <BorderBeam colorFrom='#4D8C29' colorTo='#85C028' size={60} duration={8} />
              </div>
            </div>
          </motion.div>

          {/* Форма для мобильных экранов - под изображением */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
            className='order-3 mx-auto mt-8 max-w-md lg:hidden'
          >
            <CallbackForm
              variant='compact'
              title='Выберите удобное время прямо сейчас'
              description='Оставьте номер — перезвоним за 5 минут'
              className='font-semibold'
            />
          </motion.div>
        </div>
      </div>
    </section>
  )
}
