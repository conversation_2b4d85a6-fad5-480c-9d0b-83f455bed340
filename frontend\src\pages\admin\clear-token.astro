---
import Layout from '@/layouts/Layout.astro';
---

<Layout title="Очистка токена">
  <div class="container mx-auto p-6 max-w-2xl">
    <h1 class="text-3xl font-bold mb-6">Очистка токена авторизации</h1>

    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
      <h2 class="text-xl font-semibold text-yellow-800 mb-4">
        ⚠️ Очистка токена
      </h2>
      <p class="text-yellow-700 mb-4">
        Эта страница поможет очистить сохраненный токен авторизации, если он устарел или поврежден.
      </p>
      
      <button
        id="clearToken"
        class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
      >
        Очистить токен
      </button>
      
      <div id="result" class="mt-4 hidden">
        <p class="text-green-700 font-medium">✅ Токен успешно очищен!</p>
        <p class="text-sm text-gray-600 mt-2">
          Теперь вы можете <a href="/admin" class="underline text-blue-600">перейти в админ-панель</a> и авторизоваться заново.
        </p>
      </div>
    </div>

    <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
      <h3 class="font-medium text-gray-800 mb-2">Информация о токене:</h3>
      <div class="text-sm text-gray-600 space-y-1">
        <p><strong>localStorage pb_token:</strong> <span id="localStorageToken">Проверяем...</span></p>
        <p><strong>Cookie pb_token:</strong> <span id="cookieToken">Проверяем...</span></p>
      </div>
    </div>
  </div>

  <script>
    // Проверяем наличие токенов
    function checkTokens() {
      const localToken = localStorage.getItem('pb_token');
      const cookieToken = document.cookie
        .split(';')
        .find(row => row.trim().startsWith('pb_token='))
        ?.split('=')[1];

      document.getElementById('localStorageToken').textContent = 
        localToken ? `${localToken.substring(0, 20)}...` : 'Не найден';
      
      document.getElementById('cookieToken').textContent = 
        cookieToken ? `${cookieToken.substring(0, 20)}...` : 'Не найден';
    }

    // Очистка токена
    function clearToken() {
      // Очищаем localStorage
      localStorage.removeItem('pb_token');
      
      // Очищаем cookie
      document.cookie = 'pb_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      
      // Показываем результат
      document.getElementById('result').classList.remove('hidden');
      
      // Обновляем информацию о токенах
      setTimeout(checkTokens, 100);
    }

    // Инициализация
    document.addEventListener('DOMContentLoaded', () => {
      checkTokens();
      document.getElementById('clearToken').addEventListener('click', clearToken);
    });
  </script>
</Layout>
