'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Phone, User, MessageSquare, Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import { ShineBorder } from '@/components/magicui/shine-border'
import { cn } from '@/lib/utils'
import type { CallbackFormData, ValidationErrors, CallbackApiResponse } from '@/lib/api'

interface CallbackFormProps {
  variant?: 'compact' | 'expanded'
  className?: string
  title?: string
  description?: string
  onSuccess?: (data: CallbackFormData) => void
  onError?: (error: string) => void
}

export function CallbackForm({
  variant = 'expanded',
  className,
  title,
  description,
  onSuccess,
  onError
}: CallbackFormProps) {
  const [formData, setFormData] = useState<CallbackFormData>({
    name: '',
    phone: '',
    message: ''
  })
  
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [isLoading, setIsLoading] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [submitMessage, setSubmitMessage] = useState('')

  // Валидация телефона (российские номера) - синхронизировано с API
  const validatePhone = (phone: string): boolean => {
    // Очищаем номер от всех символов кроме цифр и +
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '')

    // Проверяем российские номера: +7XXXXXXXXXX, 8XXXXXXXXXX или 7XXXXXXXXXX
    // где X - цифры, первая цифра после кода страны должна быть 4, 8 или 9
    const phoneRegex = /^(\+7|8|7)?[489][0-9]{9}$/

    return phoneRegex.test(cleanPhone)
  }

  // Валидация имени (минимум 2 символа, только буквы, пробелы и дефисы) - синхронизировано с API
  const validateName = (name: string): boolean => {
    const trimmedName = name.trim()
    return trimmedName.length >= 2 &&
           trimmedName.length <= 50 &&
           /^[а-яёА-ЯЁa-zA-Z\s\-]+$/.test(trimmedName)
  }

  // Валидация сообщения
  const validateMessage = (message: string): boolean => {
    return message.trim().length <= 1000
  }

  // Валидация формы
  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {}

    // Валидация имени с детальными сообщениями
    if (!formData.name.trim()) {
      newErrors.name = 'Имя обязательно для заполнения'
    } else if (!validateName(formData.name)) {
      const trimmedName = formData.name.trim()
      if (trimmedName.length < 2) {
        newErrors.name = 'Имя должно содержать не менее 2 символов'
      } else if (trimmedName.length > 50) {
        newErrors.name = 'Имя не должно превышать 50 символов'
      } else {
        newErrors.name = 'Имя должно содержать только буквы, пробелы и дефисы'
      }
    }

    // Валидация телефона с примером формата
    if (!formData.phone.trim()) {
      newErrors.phone = 'Телефон обязателен для заполнения'
    } else if (!validatePhone(formData.phone)) {
      newErrors.phone = 'Введите корректный российский номер телефона (например: ****** 123-45-67)'
    }

    // Валидация сообщения (опционально)
    if (formData.message && !validateMessage(formData.message)) {
      newErrors.message = 'Сообщение не должно превышать 1000 символов'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Форматирование телефона
  const formatPhone = (value: string): string => {
    const cleaned = value.replace(/\D/g, '')
    if (cleaned.length === 0) return ''
    
    let formatted = cleaned
    if (cleaned.startsWith('8')) {
      formatted = '7' + cleaned.slice(1)
    } else if (!cleaned.startsWith('7')) {
      formatted = '7' + cleaned
    }
    
    if (formatted.length > 11) {
      formatted = formatted.slice(0, 11)
    }
    
    const match = formatted.match(/^7(\d{0,3})(\d{0,3})(\d{0,2})(\d{0,2})$/)
    if (match) {
      let result = '+7'
      if (match[1]) result += ` (${match[1]}`
      if (match[2]) result += `) ${match[2]}`
      if (match[3]) result += `-${match[3]}`
      if (match[4]) result += `-${match[4]}`
      return result
    }
    
    return '+' + formatted
  }

  // Обработка изменения полей
  const handleInputChange = (field: keyof CallbackFormData, value: string) => {
    if (field === 'phone') {
      value = formatPhone(value)
    }
    
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Очистка ошибки при изменении поля
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
    
    // Сброс статуса отправки при изменении данных
    if (submitStatus !== 'idle') {
      setSubmitStatus('idle')
      setSubmitMessage('')
    }
  }

  // Отправка формы
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    setSubmitStatus('idle')

    try {
      // Заглушка API - заменить на реальный endpoint
      const response = await fetch('/api/callback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result: CallbackApiResponse = await response.json()

      if (result.success) {
        setSubmitStatus('success')
        setSubmitMessage(result.message || 'Заявка успешно отправлена! Мы свяжемся с вами в ближайшее время.')
        
        // Очистка формы
        setFormData({ name: '', phone: '', message: '' })
        setErrors({})
        
        onSuccess?.(formData)
      } else {
        throw new Error(result.message || 'Ошибка при отправке заявки')
      }
    } catch (error) {
      setSubmitStatus('error')
      const errorMessage = error instanceof Error ? error.message : 'Произошла ошибка при отправке заявки'
      setSubmitMessage(errorMessage)
      onError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const isCompact = variant === 'compact'

  const formContent = (
    <div className={cn("space-y-6", isCompact && "space-y-4")}>
      {/* Заголовки для compact варианта */}
      {isCompact && title && (
        <div className="text-center mb-4">
          <h3 className="text-lg font-bold text-gray-900 mb-1">
            {title}
          </h3>
          {description && (
            <p className="text-sm text-gray-600">
              {description}
            </p>
          )}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {/* Поле имени */}
      <div className="space-y-2">
        <Label htmlFor="name" className={cn(
          "text-sm font-medium text-gray-700",
          isCompact ? "text-xs" : "text-sm"
        )}>
          Как к вам обращаться ? *
        </Label>
        <div className="relative group">
          <User className={cn(
            "absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 transition-colors group-focus-within:text-[#8BC34A]",
            isCompact ? "h-3 w-3" : "h-4 w-4"
          )} />
          <Input
            id="name"
            type="text"
            placeholder=""
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={cn(
              "pl-10 bg-white/70 backdrop-blur-sm border-gray-200/50 focus:border-[#8BC34A] focus:ring-[#8BC34A]/20 transition-all duration-300",
              // Минимальный размер тач-элемента 44px для мобильных устройств
              isCompact ? "h-11 min-h-[44px] text-sm rounded-lg" : "h-12 text-base rounded-xl shadow-sm hover:shadow-md min-h-[44px]",
              errors.name && "border-destructive focus-visible:ring-destructive"
            )}
            aria-invalid={!!errors.name}
            aria-describedby={errors.name ? "name-error" : undefined}
          />
        </div>
        {errors.name && (
          <p id="name-error" className="text-sm text-destructive flex items-center gap-1" role="alert">
            <AlertCircle className="h-3 w-3" />
            {errors.name}
          </p>
        )}
      </div>

      {/* Поле телефона */}
      <div className="space-y-2">
        <Label htmlFor="phone" className={cn(
          "text-sm font-medium text-gray-700",
          isCompact ? "text-xs" : "text-sm"
        )}>
          Телефон *
        </Label>
        <div className="relative group">
          <Phone className={cn(
            "absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 transition-colors group-focus-within:text-[#8BC34A]",
            isCompact ? "h-3 w-3" : "h-4 w-4"
          )} />
          <Input
            id="phone"
            type="tel"
            placeholder="+7 (___) ___-__-__"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            className={cn(
              "pl-10 bg-white/70 backdrop-blur-sm border-gray-200/50 focus:border-[#8BC34A] focus:ring-[#8BC34A]/20 transition-all duration-300",
              // Минимальный размер тач-элемента 44px для мобильных устройств
              isCompact ? "h-11 min-h-[44px] text-sm rounded-lg" : "h-12 text-base rounded-xl shadow-sm hover:shadow-md min-h-[44px]",
              errors.phone && "border-destructive focus-visible:ring-destructive"
            )}
            aria-invalid={!!errors.phone}
            aria-describedby={errors.phone ? "phone-error" : undefined}
          />
        </div>
        {errors.phone && (
          <p id="phone-error" className="text-sm text-destructive flex items-center gap-1" role="alert">
            <AlertCircle className="h-3 w-3" />
            {errors.phone}
          </p>
        )}
      </div>

      {/* Поле сообщения - только для развернутой версии */}
      {!isCompact && (
        <div className="space-y-2">
          <Label htmlFor="message" className="text-sm font-medium text-gray-700">
            Комментарий
          </Label>
          <div className="relative group">
            <MessageSquare className="absolute left-3 top-3 h-4 w-4 text-gray-400 transition-colors group-focus-within:text-[#8BC34A]" />
            <Textarea
              id="message"
              placeholder="Дополнительная информация (необязательно)"
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              className={cn(
                "pl-10 min-h-[100px] resize-none bg-white/70 backdrop-blur-sm border-gray-200/50 focus:border-[#8BC34A] focus:ring-[#8BC34A]/20 transition-all duration-300 rounded-xl shadow-sm hover:shadow-md",
                errors.message && "border-destructive focus-visible:ring-destructive"
              )}
              maxLength={1000}
              aria-invalid={!!errors.message}
              aria-describedby={errors.message ? "message-error" : undefined}
            />
          </div>
          {errors.message && (
            <p id="message-error" className="text-sm text-destructive flex items-center gap-1" role="alert">
              <AlertCircle className="h-3 w-3" />
              {errors.message}
            </p>
          )}
          {formData.message && (
            <p className="text-xs text-gray-500 text-right flex items-center justify-end gap-1">
              <span className={cn(
                "transition-colors",
                formData.message.length > 800 ? "text-orange-500" : "text-gray-500",
                formData.message.length > 950 ? "text-red-500" : ""
              )}>
                {formData.message.length}/1000
              </span>
            </p>
          )}
        </div>
      )}

      {/* Статус отправки */}
      {submitStatus !== 'idle' && (
        <Alert className={cn(
          "border-0 shadow-lg backdrop-blur-sm transition-all duration-300",
          submitStatus === 'success' && "bg-gradient-to-r from-green-50/80 to-emerald-50/80 text-green-800 border-green-200/50",
          submitStatus === 'error' && "bg-gradient-to-r from-red-50/80 to-rose-50/80 text-red-800 border-red-200/50"
        )}>
          {submitStatus === 'success' ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription className="font-medium">{submitMessage}</AlertDescription>
        </Alert>
      )}

      {/* Кнопка отправки */}
      <Button
        type="submit"
        disabled={isLoading}
        className={cn(
          "w-full bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] hover:from-[#4E8C29] hover:to-[#85C026] text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] font-semibold",
          // Минимальный размер тач-элемента 44px для мобильных устройств
          isCompact ? "h-11 text-sm rounded-lg min-h-[44px]" : "h-14 text-base rounded-xl min-h-[44px]",
          isLoading && "cursor-not-allowed opacity-80"
        )}
      >
        {isLoading ? (
          <>
            <Loader2 className={cn("animate-spin", isCompact ? "mr-2 h-3 w-3" : "mr-2 h-4 w-4")} />
            Отправляем...
          </>
        ) : (
          <>
            <Phone className={cn(isCompact ? "mr-2 h-3 w-3" : "mr-2 h-4 w-4")} />
            {isCompact ? "Записаться на прием" : "Записаться на прием"}
          </>
        )}
      </Button>
      </form>
    </div>
  )

  if (isCompact) {
    return (
      <div className={cn("w-full max-w-sm", className)}>
        {formContent}
      </div>
    )
  }

  return (
    <div className={cn("relative w-full max-w-[600px] mx-auto", className)}>
      {/* Shine Border Effect */}
      <ShineBorder
        shineColor={["#8BC34A", "#4E8C29", "#85C026"]}
        duration={8}
        borderWidth={2}
        className="rounded-2xl"
      />

      {/* Glass Card with Modern Design */}
      <div className="relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-xl border border-white/20 shadow-2xl">
        {/* Gradient Background Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#8BC34A]/10 via-white/50 to-[#85C026]/10" />

        {/* Floating Orbs */}
        <div className="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-[#8BC34A]/30 to-[#85C026]/30 rounded-full blur-xl animate-pulse" />
        <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-[#4E8C29]/30 to-[#8BC34A]/30 rounded-full blur-xl animate-pulse" style={{ animationDelay: '1s' }} />

        {/* Content */}
        <div className="relative z-10 p-8">
          {/* Header with Icon */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-[#8BC34A] to-[#4E8C29] rounded-2xl shadow-lg mb-4 transform hover:scale-105 transition-transform duration-300">
              <Phone className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold bg-gradient-to-r from-[#4E8C29] to-[#8BC34A] bg-clip-text text-transparent mb-2">
              {title || 'Запишитесь прямо сейчас'}
            </h3>
            <p className="text-gray-600 text-sm leading-relaxed">
              {description || 'Оставьте номер — администратор свяжется в течение 5 минут и поможет выбрать удобное время'}
            </p>
          </div>

          {/* Form */}
          <div className="space-y-6">
            {formContent}
          </div>
        </div>

        {/* Bottom Glow */}
        <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-3/4 h-px bg-gradient-to-r from-transparent via-[#8BC34A]/50 to-transparent" />
      </div>
    </div>
  )
}
