'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Phone, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { type Promo } from '@/lib/api'

interface PromoPopupProps {
  promo: Promo | null
  isVisible: boolean
  onClose: () => void
}

export function PromoPopup({ promo, isVisible, onClose }: PromoPopupProps) {
  const [isClosing, setIsClosing] = useState(false)

  // Если нет акции, не показываем поп-ап
  if (!promo) return null

  const handleClose = () => {
    setIsClosing(true)
    setTimeout(() => {
      onClose()
      setIsClosing(false)
    }, 300)
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose()
    }
  }

  const handleCallClick = () => {
    window.location.href = 'tel:+78152525708'
    handleClose()
  }

  const handleBookingClick = () => {
    // Прокручиваем к форме записи или открываем модальное окно
    const contactSection = document.getElementById('contact-section')
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' })
    }
    handleClose()
  }

  return (
    <AnimatePresence>
      {isVisible && !isClosing && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleBackdropClick}
          />

          {/* Popup Content */}
          <motion.div
            className="relative w-full max-w-md bg-white rounded-2xl shadow-2xl overflow-hidden"
            initial={{ scale: 0.8, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.8, opacity: 0, y: 20 }}
            transition={{ 
              type: "spring", 
              stiffness: 300, 
              damping: 30,
              duration: 0.4 
            }}
          >
            {/* Close Button */}
            <button
              onClick={handleClose}
              className="absolute top-4 right-4 z-10 p-2 rounded-full bg-white/80 hover:bg-white transition-colors shadow-lg"
              aria-label="Закрыть"
            >
              <X className="h-5 w-5 text-gray-600" />
            </button>

            {/* Header with gradient */}
            <div className="bg-gradient-to-r from-olive-500 to-olive-600 p-6 text-white text-center">
              <motion.div
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <h2 className="text-2xl font-bold mb-2">{promo.title}</h2>
                {promo.subtitle && (
                  <p className="text-olive-100 text-lg">{promo.subtitle}</p>
                )}
              </motion.div>
            </div>

            {/* Content */}
            <div className="p-6">
              {promo.content && (
                <motion.div
                  className="prose prose-sm max-w-none mb-6"
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  dangerouslySetInnerHTML={{ __html: promo.content }}
                />
              )}

              {/* Action Buttons */}
              <motion.div
                className="flex flex-col gap-3"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                <Button
                  onClick={handleCallClick}
                  className="w-full bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-lg"
                  size="lg"
                >
                  <Phone className="h-5 w-5 mr-2" />
                  Позвонить сейчас
                </Button>
                
                <Button
                  onClick={handleBookingClick}
                  variant="outline"
                  className="w-full border-olive-600 text-olive-700 hover:bg-olive-50"
                  size="lg"
                >
                  <Calendar className="h-5 w-5 mr-2" />
                  Записаться онлайн
                </Button>
              </motion.div>

              {/* Timer or urgency indicator */}
              <motion.div
                className="mt-4 text-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
              >
                <p className="text-sm text-gray-600">
                  ⏰ Предложение ограничено по времени
                </p>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
