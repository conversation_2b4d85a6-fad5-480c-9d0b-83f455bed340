import type { APIRoute } from 'astro';
import PocketBase from 'pocketbase';
import { seoConfig } from '../lib/seo-config';

const pb = new PocketBase('https://pb.stom-line.ru');

export const GET: APIRoute = async () => {
  let items: any[] = [];

  try {
    // Получаем последние новости и акции
    const [news, promos] = await Promise.all([
      pb.collection('news').getList(1, 20, {
        sort: '-date',
        filter: 'status = "published"',
        fields: 'id,title,subtitle,slug,date,updated,image'
      }),
      pb.collection('promos').getList(1, 10, {
        sort: '-created',
        fields: 'id,title,subtitle,slug,created,updated,image'
      })
    ]);

    // Добавляем новости
    news.items.forEach((item: any) => {
      items.push({
        title: item.title,
        description: item.subtitle || '',
        link: `${seoConfig.site.url}/news/${item.slug}`,
        pubDate: new Date(item.date).toUTCString(),
        guid: `${seoConfig.site.url}/news/${item.slug}`,
        category: 'Новости',
        image: item.image ? `${seoConfig.site.url}/api/files/news/${item.id}/${item.image}` : null
      });
    });

    // Добавляем акции
    promos.items.forEach((item: any) => {
      items.push({
        title: item.title,
        description: item.subtitle || '',
        link: `${seoConfig.site.url}/promos/${item.slug}`,
        pubDate: new Date(item.created).toUTCString(),
        guid: `${seoConfig.site.url}/promos/${item.slug}`,
        category: 'Акции',
        image: item.image ? `${seoConfig.site.url}/api/files/promos/${item.id}/${item.image}` : null
      });
    });

    // Сортируем по дате
    items.sort((a, b) => new Date(b.pubDate).getTime() - new Date(a.pubDate).getTime());

  } catch (error) {
    console.error('Ошибка при получении данных для RSS:', error);
  }

  // Генерируем RSS XML
  const rss = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" 
     xmlns:content="http://purl.org/rss/1.0/modules/content/"
     xmlns:wfw="http://wellformedweb.org/CommentAPI/"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:atom="http://www.w3.org/2005/Atom"
     xmlns:sy="http://purl.org/rss/1.0/modules/syndication/"
     xmlns:slash="http://purl.org/rss/1.0/modules/slash/"
     xmlns:media="http://search.yahoo.com/mrss/">
  <channel>
    <title>${seoConfig.site.name} - Новости и акции</title>
    <link>${seoConfig.site.url}</link>
    <description>${seoConfig.site.description}</description>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <language>${seoConfig.site.language}</language>
    <sy:updatePeriod>daily</sy:updatePeriod>
    <sy:updateFrequency>1</sy:updateFrequency>
    <generator>Astro</generator>
    <atom:link href="${seoConfig.site.url}/rss.xml" rel="self" type="application/rss+xml"/>
    <image>
      <url>${seoConfig.organization.logo}</url>
      <title>${seoConfig.site.name}</title>
      <link>${seoConfig.site.url}</link>
      <width>144</width>
      <height>144</height>
    </image>
    <managingEditor>${seoConfig.organization.email} (${seoConfig.organization.name})</managingEditor>
    <webMaster>${seoConfig.organization.email} (${seoConfig.organization.name})</webMaster>
    <copyright>© ${new Date().getFullYear()} ${seoConfig.organization.name}</copyright>
    <category>Медицина</category>
    <category>Стоматология</category>
${items.map(item => `    <item>
      <title><![CDATA[${item.title}]]></title>
      <description><![CDATA[${item.description}]]></description>
      <link>${item.link}</link>
      <guid isPermaLink="true">${item.guid}</guid>
      <pubDate>${item.pubDate}</pubDate>
      <category><![CDATA[${item.category}]]></category>
      <dc:creator><![CDATA[${seoConfig.organization.name}]]></dc:creator>
      ${item.image ? `<media:content url="${item.image}" type="image/jpeg"/>` : ''}
      ${item.image ? `<enclosure url="${item.image}" type="image/jpeg"/>` : ''}
    </item>`).join('\n')}
  </channel>
</rss>`;

  return new Response(rss, {
    headers: {
      'Content-Type': 'application/rss+xml; charset=utf-8',
      'Cache-Control': 'public, max-age=3600'
    }
  });
};
