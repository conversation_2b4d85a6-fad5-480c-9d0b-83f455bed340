import type { APIRoute } from 'astro';
import PocketBase from 'pocketbase';

const pb = new PocketBase('https://pb.stom-line.ru');

export const GET: APIRoute = async () => {
  const baseUrl = 'https://stom-line.ru';
  
  // Статические страницы
  const staticPages = [
    { url: '', priority: '1.0', changefreq: 'daily' },
    { url: '/services', priority: '0.9', changefreq: 'weekly' },
    { url: '/specialists', priority: '0.9', changefreq: 'weekly' },
    { url: '/prices', priority: '0.8', changefreq: 'weekly' },
    { url: '/reviews', priority: '0.7', changefreq: 'weekly' },
    { url: '/news', priority: '0.7', changefreq: 'daily' },
    { url: '/promos', priority: '0.8', changefreq: 'daily' },
    { url: '/faq', priority: '0.6', changefreq: 'monthly' },
    { url: '/about', priority: '0.5', changefreq: 'monthly' }
  ];

  let dynamicPages: any[] = [];

  try {
    // Получаем динамические страницы из PocketBase
    const [services, specialists, news, promos] = await Promise.all([
      pb.collection('services').getFullList({
        fields: 'slug,updated',
        filter: 'status = "published"'
      }),
      pb.collection('doctors').getFullList({
        fields: 'slug,updated',
        filter: 'status = "published"'
      }),
      pb.collection('news').getFullList({
        fields: 'slug,updated',
        filter: 'status = "published"'
      }),
      pb.collection('promos').getFullList({
        fields: 'slug,updated'
      })
    ]);

    // Добавляем услуги
    services.forEach((service: any) => {
      dynamicPages.push({
        url: `/services/${service.slug}`,
        lastmod: service.updated,
        priority: '0.8',
        changefreq: 'monthly'
      });
    });

    // Добавляем специалистов
    specialists.forEach((specialist: any) => {
      dynamicPages.push({
        url: `/specialists/${specialist.slug}`,
        lastmod: specialist.updated,
        priority: '0.7',
        changefreq: 'monthly'
      });
    });

    // Добавляем новости
    news.forEach((newsItem: any) => {
      dynamicPages.push({
        url: `/news/${newsItem.slug}`,
        lastmod: newsItem.updated,
        priority: '0.6',
        changefreq: 'yearly'
      });
    });

    // Добавляем акции
    promos.forEach((promo: any) => {
      dynamicPages.push({
        url: `/promos/${promo.slug}`,
        lastmod: promo.updated,
        priority: '0.7',
        changefreq: 'weekly'
      });
    });

  } catch (error) {
    console.error('Ошибка при получении данных для sitemap:', error);
  }

  // Генерируем XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
${staticPages.map(page => `  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
    <mobile:mobile/>
  </url>`).join('\n')}
${dynamicPages.map(page => `  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
    <mobile:mobile/>
  </url>`).join('\n')}
</urlset>`;

  return new Response(sitemap, {
    headers: {
      'Content-Type': 'application/xml; charset=utf-8',
      'Cache-Control': 'public, max-age=3600'
    }
  });
};
