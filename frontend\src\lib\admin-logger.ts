/**
 * Система логирования действий администратора
 * Записывает все важные действия в админ панели для аудита безопасности
 */

interface AdminLogEntry {
  timestamp: string;
  userId: string;
  userEmail: string;
  action: string;
  collection?: string;
  recordId?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

class AdminLogger {
  private logs: AdminLogEntry[] = [];
  private maxLogs = 1000; // Максимальное количество логов в памяти

  /**
   * Записать действие администратора
   */
  log(action: string, details?: {
    collection?: string;
    recordId?: string;
    data?: Record<string, any>;
    error?: string;
  }) {
    try {
      const user = this.getCurrentUser();
      if (!user) return;

      const logEntry: AdminLogEntry = {
        timestamp: new Date().toISOString(),
        userId: user.id,
        userEmail: user.email,
        action,
        collection: details?.collection,
        recordId: details?.recordId,
        details: details?.data,
        ipAddress: this.getClientIP(),
        userAgent: navigator.userAgent
      };

      // Добавляем в локальный массив
      this.logs.unshift(logEntry);
      
      // Ограничиваем размер массива
      if (this.logs.length > this.maxLogs) {
        this.logs = this.logs.slice(0, this.maxLogs);
      }

      // Сохраняем в localStorage для персистентности
      this.saveToStorage();

      // В продакшене можно отправлять на сервер
      if (!import.meta.env.DEV) {
        this.sendToServer(logEntry);
      }

      console.log(`[ADMIN LOG] ${action}`, logEntry);
    } catch (error) {
      console.error('Ошибка записи лога:', error);
    }
  }

  /**
   * Получить текущего пользователя
   */
  private getCurrentUser() {
    try {
      const token = localStorage.getItem('pb_token');
      if (!token) return null;

      // В реальном приложении нужно декодировать JWT токен
      // Пока используем заглушку
      return {
        id: 'admin-user',
        email: '<EMAIL>'
      };
    } catch {
      return null;
    }
  }

  /**
   * Получить IP адрес клиента (приблизительно)
   */
  private getClientIP(): string {
    // В браузере нельзя получить реальный IP, но можно попробовать через WebRTC
    return 'unknown';
  }

  /**
   * Сохранить логи в localStorage
   */
  private saveToStorage() {
    try {
      const recentLogs = this.logs.slice(0, 100); // Сохраняем только последние 100
      localStorage.setItem('admin_logs', JSON.stringify(recentLogs));
    } catch (error) {
      console.error('Ошибка сохранения логов:', error);
    }
  }

  /**
   * Загрузить логи из localStorage
   */
  private loadFromStorage() {
    try {
      const stored = localStorage.getItem('admin_logs');
      if (stored) {
        this.logs = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Ошибка загрузки логов:', error);
      this.logs = [];
    }
  }

  /**
   * Отправить лог на сервер (для продакшена)
   */
  private async sendToServer(logEntry: AdminLogEntry) {
    try {
      // В реальном приложении отправляем на специальный endpoint для логов
      await fetch('/api/admin/logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('pb_token')}`
        },
        body: JSON.stringify(logEntry)
      });
    } catch (error) {
      console.error('Ошибка отправки лога на сервер:', error);
    }
  }

  /**
   * Получить все логи
   */
  getLogs(): AdminLogEntry[] {
    return [...this.logs];
  }

  /**
   * Получить логи за определенный период
   */
  getLogsByDateRange(startDate: Date, endDate: Date): AdminLogEntry[] {
    return this.logs.filter(log => {
      const logDate = new Date(log.timestamp);
      return logDate >= startDate && logDate <= endDate;
    });
  }

  /**
   * Получить логи по действию
   */
  getLogsByAction(action: string): AdminLogEntry[] {
    return this.logs.filter(log => log.action === action);
  }

  /**
   * Получить логи по коллекции
   */
  getLogsByCollection(collection: string): AdminLogEntry[] {
    return this.logs.filter(log => log.collection === collection);
  }

  /**
   * Очистить логи
   */
  clearLogs() {
    this.logs = [];
    localStorage.removeItem('admin_logs');
    this.log('LOGS_CLEARED', { data: { reason: 'Manual clear' } });
  }

  /**
   * Экспортировать логи в CSV
   */
  exportToCSV(): string {
    const headers = ['Время', 'Пользователь', 'Действие', 'Коллекция', 'ID записи', 'IP адрес'];
    const rows = this.logs.map(log => [
      log.timestamp,
      log.userEmail,
      log.action,
      log.collection || '',
      log.recordId || '',
      log.ipAddress || ''
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    return csvContent;
  }

  /**
   * Инициализация логгера
   */
  init() {
    this.loadFromStorage();
    this.log('LOGGER_INITIALIZED');
  }
}

// Создаем единственный экземпляр логгера
export const adminLogger = new AdminLogger();

// Автоматически инициализируем при импорте
if (typeof window !== 'undefined') {
  adminLogger.init();
}

// Предопределенные действия для логирования
export const AdminActions = {
  // Авторизация
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  LOGIN_FAILED: 'LOGIN_FAILED',

  // CRUD операции
  CREATE_RECORD: 'CREATE_RECORD',
  UPDATE_RECORD: 'UPDATE_RECORD',
  DELETE_RECORD: 'DELETE_RECORD',
  VIEW_RECORD: 'VIEW_RECORD',
  LIST_RECORDS: 'LIST_RECORDS',

  // Файлы
  UPLOAD_FILE: 'UPLOAD_FILE',
  DELETE_FILE: 'DELETE_FILE',
  DOWNLOAD_FILE: 'DOWNLOAD_FILE',

  // Системные действия
  CLEAR_CACHE: 'CLEAR_CACHE',
  EXPORT_DATA: 'EXPORT_DATA',
  IMPORT_DATA: 'IMPORT_DATA',
  BACKUP_CREATED: 'BACKUP_CREATED',

  // Безопасность
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
  PASSWORD_CHANGED: 'PASSWORD_CHANGED',

  // Ошибки
  ERROR: 'ERROR',
  CRITICAL_ERROR: 'CRITICAL_ERROR'
} as const;

// Хелперы для быстрого логирования
export const logAdminAction = {
  login: (email: string) => adminLogger.log(AdminActions.LOGIN, { data: { email } }),
  logout: () => adminLogger.log(AdminActions.LOGOUT),
  createRecord: (collection: string, recordId: string, data?: any) => 
    adminLogger.log(AdminActions.CREATE_RECORD, { collection, recordId, data }),
  updateRecord: (collection: string, recordId: string, data?: any) => 
    adminLogger.log(AdminActions.UPDATE_RECORD, { collection, recordId, data }),
  deleteRecord: (collection: string, recordId: string) => 
    adminLogger.log(AdminActions.DELETE_RECORD, { collection, recordId }),
  error: (error: string, details?: any) => 
    adminLogger.log(AdminActions.ERROR, { data: { error, details } })
};
