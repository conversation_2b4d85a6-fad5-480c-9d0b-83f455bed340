/**
 * Пример интеграции API callback с PocketBase
 * Этот файл показывает, как обновить frontend/src/pages/api/callback.ts
 * для работы с реальной коллекцией callback_requests
 */

import type { APIRoute } from 'astro';
import pb from '@/lib/pocketbase';
import type { CallbackFormData, CallbackApiResponse } from '@/lib/api';

// Интерфейс для данных запроса
interface CallbackRequestData {
  name: string;
  phone: string;
  message?: string;
  source?: string;
}

// Интерфейс для валидации
interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Валидация данных формы
 */
const validateFormData = (data: any): ValidationResult => {
  const errors: string[] = [];

  // Проверка имени
  if (!data.name || typeof data.name !== 'string') {
    errors.push('Имя обязательно для заполнения');
  } else if (data.name.trim().length < 2) {
    errors.push('Имя должно содержать минимум 2 символа');
  } else if (data.name.trim().length > 100) {
    errors.push('Имя не должно превышать 100 символов');
  }

  // Проверка телефона
  if (!data.phone || typeof data.phone !== 'string') {
    errors.push('Номер телефона обязателен для заполнения');
  } else {
    const phoneRegex = /^[+]?[0-9\s\-\(\)]{10,20}$/;
    if (!phoneRegex.test(data.phone.trim())) {
      errors.push('Некорректный формат номера телефона');
    }
  }

  // Проверка сообщения (опционально)
  if (data.message && typeof data.message === 'string' && data.message.trim().length > 1000) {
    errors.push('Сообщение не должно превышать 1000 символов');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Получение IP-адреса клиента
 */
const getClientIP = (request: Request): string => {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('remote-addr');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  return realIP || remoteAddr || 'unknown';
};

/**
 * Определение источника запроса
 */
const getRequestSource = (request: Request): string => {
  const referer = request.headers.get('referer');
  const userAgent = request.headers.get('user-agent');
  
  if (!referer) return 'direct';
  
  try {
    const url = new URL(referer);
    const pathname = url.pathname;
    
    // Определяем источник по URL
    if (pathname === '/') return 'homepage-form';
    if (pathname.startsWith('/services')) return 'services-page-form';
    if (pathname.startsWith('/specialists')) return 'specialists-page-form';
    if (pathname.startsWith('/contacts')) return 'contacts-page-form';
    if (pathname.startsWith('/promos')) return 'promo-page-form';
    
    return `${pathname}-form`;
  } catch {
    return 'unknown-page';
  }
};

/**
 * Сохранение заявки в PocketBase
 */
const saveCallbackRequest = async (
  data: CallbackFormData, 
  request: Request
): Promise<{ success: boolean; id?: string; error?: string }> => {
  try {
    // Подготавливаем данные для сохранения
    const recordData = {
      name: data.name.trim(),
      phone: data.phone.trim(),
      message: data.message?.trim() || '',
      status: 'new',
      source: getRequestSource(request),
      ip_address: getClientIP(request),
      user_agent: request.headers.get('user-agent') || ''
    };

    // Создаем запись в PocketBase
    const record = await pb.collection('callback_requests').create(recordData);

    console.log('Заявка на обратный звонок сохранена:', {
      id: record.id,
      name: recordData.name,
      phone: recordData.phone,
      source: recordData.source,
      timestamp: record.created
    });

    return {
      success: true,
      id: record.id
    };
  } catch (error) {
    console.error('Ошибка при сохранении заявки в PocketBase:', error);
    
    // Обработка специфичных ошибок PocketBase
    if (error.response?.data) {
      const pbError = error.response.data;
      return {
        success: false,
        error: `Ошибка валидации: ${JSON.stringify(pbError)}`
      };
    }
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Неизвестная ошибка при сохранении'
    };
  }
};

/**
 * Отправка уведомлений
 */
const sendNotification = async (data: CallbackFormData, requestId: string): Promise<void> => {
  try {
    // Здесь можно добавить различные способы уведомлений:
    
    // 1. Email уведомление администратору
    // await sendEmailNotification(data, requestId);
    
    // 2. SMS уведомление
    // await sendSMSNotification(data, requestId);
    
    // 3. Webhook в CRM систему
    // await sendCRMWebhook(data, requestId);
    
    // 4. Уведомление в Telegram/Slack
    // await sendTelegramNotification(data, requestId);
    
    console.log('Уведомления отправлены для заявки:', requestId);
    
  } catch (error) {
    console.error('Ошибка при отправке уведомлений:', error);
    // Не прерываем выполнение, если уведомления не отправились
  }
};

/**
 * Rate limiting (простая реализация)
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

const checkRateLimit = (ip: string): boolean => {
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 минут
  const maxRequests = 5; // максимум 5 запросов за 15 минут
  
  const current = rateLimitMap.get(ip);
  
  if (!current || now > current.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxRequests) {
    return false;
  }
  
  current.count++;
  return true;
};

/**
 * Основной обработчик POST запросов
 */
export const POST: APIRoute = async ({ request }) => {
  try {
    // Проверка Content-Type
    const contentType = request.headers.get('content-type');
    if (!contentType?.includes('application/json')) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Content-Type должен быть application/json'
        } as CallbackApiResponse),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Rate limiting
    const clientIP = getClientIP(request);
    if (!checkRateLimit(clientIP)) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Слишком много запросов. Попробуйте позже.'
        } as CallbackApiResponse),
        {
          status: 429,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Парсинг данных
    let requestData: CallbackRequestData;
    try {
      requestData = await request.json();
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Некорректный JSON в теле запроса'
        } as CallbackApiResponse),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Валидация данных
    const validation = validateFormData(requestData);
    if (!validation.isValid) {
      return new Response(
        JSON.stringify({
          success: false,
          message: `Ошибки валидации: ${validation.errors.join(', ')}`
        } as CallbackApiResponse),
        {
          status: 422,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Подготовка данных для сохранения
    const formData: CallbackFormData = {
      name: requestData.name.trim(),
      phone: requestData.phone.trim(),
      message: requestData.message?.trim() || ''
    };

    // Сохранение заявки
    const saveResult = await saveCallbackRequest(formData, request);
    
    if (!saveResult.success) {
      return new Response(
        JSON.stringify({
          success: false,
          message: saveResult.error || 'Ошибка при сохранении заявки'
        } as CallbackApiResponse),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Отправка уведомлений (асинхронно)
    if (saveResult.id) {
      sendNotification(formData, saveResult.id).catch(console.error);
    }

    // Успешный ответ
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Заявка успешно отправлена! Мы свяжемся с вами в ближайшее время.',
        id: saveResult.id
      } as CallbackApiResponse),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Неожиданная ошибка в API callback:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Внутренняя ошибка сервера'
      } as CallbackApiResponse),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

/**
 * Обработчик GET запросов
 */
export const GET: APIRoute = async () => {
  return new Response(
    JSON.stringify({
      success: false,
      message: 'Метод GET не поддерживается. Используйте POST для отправки заявки.'
    } as CallbackApiResponse),
    {
      status: 405,
      headers: {
        'Content-Type': 'application/json',
        'Allow': 'POST'
      }
    }
  );
};
