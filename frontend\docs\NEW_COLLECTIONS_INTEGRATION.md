# Интеграция новых коллекций PocketBase

## Обзор

В систему администрирования контента были успешно интегрированы две новые коллекции PocketBase:

1. **html_blocks** - для хранения HTML-блоков, используемых на страницах Astro
2. **personal** - для описания сотрудников клиники, которые не являются врачами

## Структура коллекций

### html_blocks
- `id` (string) - уникальный идентификатор
- `content` (editor) - HTML-контент блока
- `created` (autodate) - дата создания
- `updated` (autodate) - дата обновления

### personal
- `id` (string) - уникальный идентификатор
- `surname` (text) - фамилия сотрудника
- `name` (text) - имя сотрудника
- `patronymic` (text) - отчество сотрудника
- `about` (editor) - описание сотрудника
- `position` (text) - должность
- `created` (autodate) - дата создания
- `updated` (autodate) - дата обновления

## Внесенные изменения

### 1. Обновление статистики коллекций
**Файл:** `frontend/src/lib/pocketbase-admin.ts`
- Добавлены новые коллекции в массив для получения статистики

### 2. Обновление админ-панели
**Файл:** `frontend/src/components/admin/AdminDashboard.tsx`
- Добавлены новые коллекции в интерфейс `CollectionStats`
- Добавлены карточки для новых коллекций с соответствующими иконками:
  - `html_blocks` - иконка `Code` (cyan-500)
  - `personal` - иконка `UserCheck` (teal-500)

### 3. Универсальный редактор записей
**Файл:** `frontend/src/components/admin/UniversalRecordEditor.tsx`
- Добавлена конфигурация полей для быстрого редактирования:
  - `html_blocks`: ['content']
  - `personal`: ['surname', 'name', 'patronymic', 'position', 'about']

### 4. Менеджер коллекций
**Файл:** `frontend/src/components/admin/CollectionManager.tsx`
- Добавлены отображаемые поля:
  - `html_blocks`: ['content']
  - `personal`: ['surname', 'name', 'position']
- Добавлены иконки для новых коллекций

### 5. Быстрое редактирование
**Файл:** `frontend/src/components/admin/QuickEditModal.tsx`
- Добавлена конфигурация полей для быстрого редактирования новых коллекций

### 6. Синхронизация с MeiliSearch
**Файл:** `backend/hooks/sync-meilisearch.js`
- Добавлены новые коллекции в список для синхронизации с поисковым движком

### 7. Страницы администрирования
Созданы новые страницы:
- `frontend/src/pages/admin/html_blocks.astro`
- `frontend/src/pages/admin/personal.astro`

### 8. Типизация
**Файл:** `frontend/src/lib/pocketbase-types.ts`
- Автоматически сгенерированы TypeScript интерфейсы для новых коллекций
- Обновлен union тип `CollectionName`

**Файл:** `frontend/src/lib/api.ts`
- Добавлены экспорты типов для новых коллекций

## Использование

### Доступ к админ-панели
1. Перейдите на `/admin`
2. Авторизуйтесь
3. Найдите новые разделы "HTML-блоки" и "Персонал"

### HTML-блоки
- Используются для хранения переиспользуемых HTML-фрагментов
- Поддерживают полнофункциональный HTML-редактор Tiptap
- Могут быть встроены в страницы Astro

### Персонал
- Предназначены для описания не-врачебного персонала
- Поддерживают полные ФИО, должность и описание
- HTML-редактор для поля "О сотруднике"

## Тестирование

Созданы тесты для проверки интеграции:
**Файл:** `frontend/src/tests/new-collections.test.tsx`

Запуск тестов:
```bash
bun test src/tests/new-collections.test.tsx
```

## Автоматическая генерация типов

Создан скрипт для автоматической генерации TypeScript типов из схемы PocketBase:
**Файл:** `frontend/scripts/generate-types.js`

Запуск генерации:
```bash
cd frontend && bun scripts/generate-types.js
```

## Корпоративные цвета

Новые коллекции используют корпоративную цветовую схему:
- Основной зеленый: `#8BC34A`
- Темно-зеленый: `#4E8C29`
- Светло-зеленый: `#85C026`
- HTML-блоки: cyan-500
- Персонал: teal-500

## Следующие шаги

1. Создание записей в новых коллекциях через админ-панель
2. Интеграция HTML-блоков в страницы Astro
3. Создание публичных страниц для отображения персонала
4. Настройка поиска по новым коллекциям в MeiliSearch

## Поддержка

Все новые коллекции полностью интегрированы в существующую систему администрирования и поддерживают:
- Полную типизацию TypeScript
- HTML-редактор Tiptap с корпоративными цветами
- Автосохранение
- Поиск и фильтрацию
- Связи между коллекциями
- Загрузку файлов через PocketBase
- Адаптивный дизайн
- Авторизацию через middleware
