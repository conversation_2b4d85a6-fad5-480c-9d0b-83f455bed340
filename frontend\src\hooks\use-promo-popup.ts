'use client'

import { useState, useEffect, useCallback } from 'react'
import { type Promo } from '@/lib/api'

interface UsePromoPopupOptions {
  promo: Promo | null
  scrollThreshold?: number // Процент прокрутки для показа поп-апа (по умолчанию 50%)
  showOnExit?: boolean // Показывать при попытке покинуть сайт
  showOnScroll?: boolean // Показывать при прокрутке
  cooldownHours?: number // Часы до повторного показа (по умолчанию 24)
}

export function usePromoPopup({
  promo,
  scrollThreshold = 50,
  showOnExit = true,
  showOnScroll = true,
  cooldownHours = 24
}: UsePromoPopupOptions) {
  const [isVisible, setIsVisible] = useState(false)
  const [hasShownOnScroll, setHasShownOnScroll] = useState(false)
  const [hasShownOnExit, setHasShownOnExit] = useState(false)

  // Ключи для localStorage
  const STORAGE_KEY = 'promo-popup-shown'
  const STORAGE_TIMESTAMP_KEY = 'promo-popup-timestamp'

  // Проверяем, можно ли показать поп-ап
  const canShowPopup = useCallback(() => {
    if (!promo || !promo.is_active) return false

    // Проверяем, что мы на клиентской стороне
    if (typeof window === 'undefined') return false

    try {
      const lastShown = localStorage.getItem(STORAGE_TIMESTAMP_KEY)
      if (lastShown) {
        const lastShownTime = new Date(lastShown).getTime()
        const now = new Date().getTime()
        const hoursPassed = (now - lastShownTime) / (1000 * 60 * 60)

        if (hoursPassed < cooldownHours) {
          console.log(`Поп-ап был показан ${Math.round(hoursPassed)} часов назад. Ждем ${cooldownHours} часов.`)
          return false
        }
      }
    } catch (error) {
      console.warn('Ошибка при проверке localStorage:', error)
    }

    return true
  }, [promo, cooldownHours])

  // Показать поп-ап
  const showPopup = useCallback(() => {
    if (typeof window === 'undefined') return

    if (canShowPopup() && !isVisible) {
      setIsVisible(true)
      try {
        localStorage.setItem(STORAGE_TIMESTAMP_KEY, new Date().toISOString())
      } catch (error) {
        console.warn('Ошибка при сохранении в localStorage:', error)
      }
    }
  }, [canShowPopup, isVisible])

  // Закрыть поп-ап
  const closePopup = useCallback(() => {
    setIsVisible(false)
  }, [])

  // Обработчик прокрутки
  const handleScroll = useCallback(() => {
    if (!showOnScroll || hasShownOnScroll || isVisible) return

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
    const scrollPercent = (scrollTop / scrollHeight) * 100

    if (scrollPercent >= scrollThreshold) {
      setHasShownOnScroll(true)
      showPopup()
    }
  }, [showOnScroll, hasShownOnScroll, isVisible, scrollThreshold, showPopup])

  // Обработчик попытки покинуть сайт
  const handleBeforeUnload = useCallback((e: BeforeUnloadEvent) => {
    if (!showOnExit || hasShownOnExit || isVisible) return

    setHasShownOnExit(true)
    showPopup()
    
    // Предотвращаем закрытие страницы, чтобы показать поп-ап
    e.preventDefault()
    e.returnValue = ''
  }, [showOnExit, hasShownOnExit, isVisible, showPopup])

  // Обработчик движения мыши к верхней части экрана (exit intent)
  const handleMouseLeave = useCallback((e: MouseEvent) => {
    if (!showOnExit || hasShownOnExit || isVisible) return

    // Если мышь движется к верхней части экрана (выход из страницы)
    if (e.clientY <= 0) {
      setHasShownOnExit(true)
      showPopup()
    }
  }, [showOnExit, hasShownOnExit, isVisible, showPopup])

  // Устанавливаем обработчики событий
  useEffect(() => {
    if (!promo || typeof window === 'undefined') return

    let scrollTimeout: NodeJS.Timeout

    const throttledScroll = () => {
      if (scrollTimeout) clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(handleScroll, 100)
    }

    // Добавляем обработчики
    if (showOnScroll) {
      window.addEventListener('scroll', throttledScroll, { passive: true })
    }

    if (showOnExit) {
      window.addEventListener('beforeunload', handleBeforeUnload)
      document.addEventListener('mouseleave', handleMouseLeave)
    }

    // Очистка при размонтировании
    return () => {
      if (scrollTimeout) clearTimeout(scrollTimeout)
      window.removeEventListener('scroll', throttledScroll)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [promo, showOnScroll, showOnExit, handleScroll, handleBeforeUnload, handleMouseLeave])

  // Сброс состояния при изменении акции
  useEffect(() => {
    setHasShownOnScroll(false)
    setHasShownOnExit(false)
    setIsVisible(false)
  }, [promo?.id])

  return {
    isVisible,
    showPopup,
    closePopup,
    canShowPopup: canShowPopup()
  }
}
