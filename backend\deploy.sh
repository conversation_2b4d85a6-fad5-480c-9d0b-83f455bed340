#!/bin/bash

# Скрипт для деплоя PocketBase в облако
# Использование: ./deploy.sh [tag]

set -e

# Настройки
IMAGE_NAME=${IMAGE_NAME:-"pocketbase-app"}
TAG=${1:-"latest"}

echo "🚀 Начинаем деплой PocketBase..."

# Проверяем, что мы в правильной директории
if [ ! -f "Dockerfile" ]; then
    echo "❌ Ошибка: Dockerfile не найден. Убедитесь, что вы в директории backend/"
    exit 1
fi

# Проверяем, что папка hooks существует
if [ ! -d "hooks" ]; then
    echo "❌ Ошибка: Папка hooks не найдена"
    exit 1
fi

echo "📦 Собираем Docker образ..."
docker build -t ${IMAGE_NAME}:${TAG} .

echo "🔍 Проверяем, что хуки включены в образ..."
docker run --rm ${IMAGE_NAME}:${TAG} ls -la /pb/pb_hooks/

echo "🔍 Проверяем, что данные НЕ включены в образ..."
if docker run --rm ${IMAGE_NAME}:${TAG} ls /pb/pb_data/ 2>/dev/null | grep -q ".db"; then
    echo "⚠️  ВНИМАНИЕ: Данные найдены в образе! Они могут быть перезатерты."
else
    echo "✅ Данные не включены в образ - будут сохраняться в volume"
fi

echo "✅ Сборка завершена!"
echo "📋 Образ: ${IMAGE_NAME}:${TAG}"
echo ""
echo "🔧 Для запуска используйте:"
echo "docker-compose -f docker-compose.prod.yml up -d"
echo ""
echo "🔒 ВАЖНО: Данные сохраняются в volume 'pocketbase_data' и НЕ перезатираются при обновлении!"
