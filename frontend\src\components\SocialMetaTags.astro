---
// Расширенные мета-теги для социальных сетей
export interface Props {
  title: string;
  description: string;
  image: string;
  url: string;
  type?: string;
  siteName?: string;
  locale?: string;
  article?: {
    publishedTime?: string;
    modifiedTime?: string;
    author?: string;
    section?: string;
    tags?: string[];
  };
}

const {
  title,
  description,
  image,
  url,
  type = 'website',
  siteName = 'STOM-LINE',
  locale = 'ru_RU',
  article
} = Astro.props;

// Формируем полные URL
const fullImageUrl = image.startsWith('http') ? image : `https://stom-line.ru${image}`;
const fullUrl = url.startsWith('http') ? url : `https://stom-line.ru${url}`;
---

<!-- Open Graph мета-теги -->
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:type" content={type} />
<meta property="og:url" content={fullUrl} />
<meta property="og:image" content={fullImageUrl} />
<meta property="og:image:alt" content={title} />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
<meta property="og:site_name" content={siteName} />
<meta property="og:locale" content={locale} />

{article && (
  <>
    <meta property="article:published_time" content={article.publishedTime} />
    <meta property="article:modified_time" content={article.modifiedTime} />
    <meta property="article:author" content={article.author} />
    <meta property="article:section" content={article.section} />
    {article.tags?.map(tag => (
      <meta property="article:tag" content={tag} />
    ))}
  </>
)}

<!-- Twitter Card мета-теги -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={title} />
<meta name="twitter:description" content={description} />
<meta name="twitter:image" content={fullImageUrl} />
<meta name="twitter:image:alt" content={title} />
<meta name="twitter:site" content="@stomline" />
<meta name="twitter:creator" content="@stomline" />

<!-- VKontakte мета-теги -->
<meta property="vk:title" content={title} />
<meta property="vk:description" content={description} />
<meta property="vk:image" content={fullImageUrl} />
<meta property="vk:url" content={fullUrl} />

<!-- Telegram мета-теги -->
<meta property="telegram:channel" content="@stomline" />
<meta property="telegram:title" content={title} />
<meta property="telegram:description" content={description} />
<meta property="telegram:image" content={fullImageUrl} />

<!-- WhatsApp мета-теги -->
<meta property="whatsapp:title" content={title} />
<meta property="whatsapp:description" content={description} />
<meta property="whatsapp:image" content={fullImageUrl} />

<!-- Viber мета-теги -->
<meta property="viber:title" content={title} />
<meta property="viber:description" content={description} />
<meta property="viber:image" content={fullImageUrl} />

<!-- Одноклассники мета-теги -->
<meta property="ok:title" content={title} />
<meta property="ok:description" content={description} />
<meta property="ok:image" content={fullImageUrl} />

<!-- LinkedIn мета-теги -->
<meta property="linkedin:title" content={title} />
<meta property="linkedin:description" content={description} />
<meta property="linkedin:image" content={fullImageUrl} />

<!-- Pinterest мета-теги -->
<meta property="pinterest:title" content={title} />
<meta property="pinterest:description" content={description} />
<meta property="pinterest:image" content={fullImageUrl} />

<!-- Мета-теги для мессенджеров -->
<meta name="format-detection" content="telephone=yes" />
<meta name="format-detection" content="address=yes" />

<!-- Дополнительные мета-теги для поисковых систем -->
<meta name="apple-mobile-web-app-title" content={siteName} />
<meta name="application-name" content={siteName} />
<meta name="msapplication-TileColor" content="#8BC34A" />
<meta name="msapplication-config" content="/browserconfig.xml" />

<!-- Мета-теги для Яндекс.Браузера -->
<meta name="yandex-verification" content="" />
<meta name="yandex-tableau-widget" content="logo=https://stom-line.ru/logo.svg, color=#8BC34A" />

<!-- Мета-теги для Google -->
<meta name="google-site-verification" content="" />

<!-- Мета-теги для Mail.ru -->
<meta name="mailru-verification" content="" />

<!-- Мета-теги для Bing -->
<meta name="msvalidate.01" content="" />

<!-- Дополнительные мета-теги для социальных сетей -->
<meta name="referrer" content="origin-when-cross-origin" />
<meta name="color-scheme" content="light" />
<meta name="supported-color-schemes" content="light" />

<!-- Мета-теги для мобильных приложений -->
<meta name="mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />

<!-- Мета-теги для безопасности -->
<meta http-equiv="X-Content-Type-Options" content="nosniff" />
<meta http-equiv="X-Frame-Options" content="SAMEORIGIN" />
<meta http-equiv="X-XSS-Protection" content="1; mode=block" />

<!-- Мета-теги для производительности -->
<meta http-equiv="Accept-CH" content="DPR, Viewport-Width, Width" />

<!-- Альтернативные ссылки для разных языков -->
<link rel="alternate" hreflang="ru" href={fullUrl} />
<link rel="alternate" hreflang="x-default" href={fullUrl} />

<!-- Ссылки для социальных сетей -->
<link rel="me" href="https://vk.com/stomline" />
<link rel="me" href="https://t.me/stomline" />
<link rel="me" href="https://instagram.com/stomline" />

<!-- Дополнительные ссылки -->
<link rel="author" href="https://stom-line.ru/about" />
<link rel="publisher" href="https://stom-line.ru" />
<link rel="copyright" href="https://stom-line.ru" />
