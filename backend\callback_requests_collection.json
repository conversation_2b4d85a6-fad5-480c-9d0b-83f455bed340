[{"id": "callback_requests", "name": "callback_requests", "type": "base", "system": false, "schema": [{"id": "name", "name": "name", "type": "text", "system": false, "required": true, "presentable": true, "unique": false, "options": {"min": 2, "max": 100, "pattern": ""}}, {"id": "phone", "name": "phone", "type": "text", "system": false, "required": true, "presentable": false, "unique": false, "options": {"min": 10, "max": 20, "pattern": "^[+]?[0-9\\s\\-\\(\\)]{10,20}$"}}, {"id": "message", "name": "message", "type": "text", "system": false, "required": false, "presentable": false, "unique": false, "options": {"min": 0, "max": 1000, "pattern": ""}}, {"id": "status", "name": "status", "type": "select", "system": false, "required": true, "presentable": false, "unique": false, "options": {"maxSelect": 1, "values": ["new", "in_progress", "completed", "cancelled"]}}, {"id": "source", "name": "source", "type": "text", "system": false, "required": false, "presentable": false, "unique": false, "options": {"min": 0, "max": 100, "pattern": ""}}, {"id": "ip_address", "name": "ip_address", "type": "text", "system": false, "required": false, "presentable": false, "unique": false, "options": {"min": 0, "max": 45, "pattern": ""}}, {"id": "user_agent", "name": "user_agent", "type": "text", "system": false, "required": false, "presentable": false, "unique": false, "options": {"min": 0, "max": 500, "pattern": ""}}, {"id": "processed_at", "name": "processed_at", "type": "date", "system": false, "required": false, "presentable": false, "unique": false, "options": {"min": "", "max": ""}}, {"id": "processed_by", "name": "processed_by", "type": "text", "system": false, "required": false, "presentable": false, "unique": false, "options": {"min": 0, "max": 100, "pattern": ""}}, {"id": "notes", "name": "notes", "type": "text", "system": false, "required": false, "presentable": false, "unique": false, "options": {"min": 0, "max": 2000, "pattern": ""}}], "indexes": ["CREATE INDEX idx_callback_requests_status ON callback_requests (status)", "CREATE INDEX idx_callback_requests_created ON callback_requests (created)", "CREATE INDEX idx_callback_requests_phone ON callback_requests (phone)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}]