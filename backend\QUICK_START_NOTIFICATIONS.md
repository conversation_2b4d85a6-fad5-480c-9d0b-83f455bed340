# 🚀 Быстрый запуск уведомлений

## 📋 Что это?

Автоматические уведомления на email и в Telegram при новых заявках на обратный звонок.

## ⚡ Быстрая настройка (5 минут)

### 1. Скопируйте настройки
```bash
cp .env.example .env
```

### 2. Настройте Email (Gmail)
Откройте `.env` и заполните:
```bash
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password  # Пароль приложения из Google
NOTIFICATION_EMAIL_TO=<EMAIL>
```

### 3. Настройте Telegram (опционально)
```bash
TELEGRAM_BOT_TOKEN=**********:ABC...  # От @BotFather
TELEGRAM_CHAT_ID=*********            # Ваш ID от @userinfobot
```

### 4. Запустите
```bash
docker-compose up --build
```

### 5. Протестируйте
```bash
node test-notifications.js
```

## 📧 Получение пароля приложения Gmail

1. Включите 2FA в Google аккаунте
2. Перейдите: https://myaccount.google.com/apppasswords
3. Выберите "Почта" → "Другое устройство"
4. Скопируйте пароль в `SMTP_PASSWORD`

## 📱 Создание Telegram бота

1. Напишите @BotFather
2. Отправьте `/newbot`
3. Следуйте инструкциям
4. Скопируйте токен в `TELEGRAM_BOT_TOKEN`

## 🆔 Получение Telegram Chat ID

1. Напишите @userinfobot
2. Отправьте любое сообщение
3. Скопируйте ID в `TELEGRAM_CHAT_ID`

## ✅ Готово!

Теперь при каждой новой заявке вы будете получать уведомления:

**Email:**
```
🔔 Новая заявка на обратный звонок - Иван Петров

👤 Имя: Иван Петров
📱 Телефон: +7 (999) 123-45-67
💬 Сообщение: Хочу записаться на консультацию
⏰ Время: 05.07.2024, 15:30
```

**Telegram:**
```
🔔 Новая заявка на обратный звонок

👤 Имя: Иван Петров
📱 Телефон: +7 (999) 123-45-67
💬 Сообщение: Хочу записаться на консультацию
⏰ Время: 05.07.2024, 15:30
```

## 🔧 Если что-то не работает

1. **Проверьте логи:**
   ```bash
   docker logs backend-pocketbase-1 -f
   ```

2. **Проверьте переменные окружения:**
   ```bash
   docker exec backend-pocketbase-1 env | grep SMTP
   docker exec backend-pocketbase-1 env | grep TELEGRAM
   ```

3. **Создайте тестовую заявку:**
   ```bash
   node test-notifications.js
   ```

## 📖 Подробная документация

Смотрите `NOTIFICATIONS_SETUP.md` для детальных инструкций и устранения проблем.
