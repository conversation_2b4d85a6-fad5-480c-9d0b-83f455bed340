---
import Layout from '@/layouts/Layout.astro'
import DocumentsPage from '@/components/documents-page'
import { getPageSEO } from '@/lib/seo-config'
import { isUserAuthenticated } from '@/middleware/auth'
import PocketBase from 'pocketbase'
import type { Files } from '@/lib/pocketbase-types'

// URL API
const PUBLIC_API_URL = 'https://pb.stom-line.ru'

// Получаем SEO данные для страницы документов
const seo = getPageSEO('documents');

// Проверяем авторизацию пользователя
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Получаем документы из PocketBase
let documents: Files[] = [];

try {
  const pb = new PocketBase(PUBLIC_API_URL);

  // Получаем документы с типом "document" или "certificate" с расширением данных о враче
  const response = await pb.collection('files').getFullList({
    filter: 'type = "document" || type = "certificate"',
    sort: '-created',
    expand: 'doctor', // Расширяем данные о враче
    requestKey: null, // Отключаем дедупликацию запросов
  });

  console.log('Получено документов из PocketBase:', response?.length || 0);

  if (response && response.length > 0) {
    documents = response;
  } else {
    console.warn('Нет документов в PocketBase');
  }
} catch (error) {
  console.error('Ошибка при получении документов:', error);
}
---

<Layout
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}
  type={seo.type}
>
  <main>
    <DocumentsPage 
      documents={documents} 
      isAuthenticated={isAuthenticated} 
      client:load 
    />
  </main>
</Layout>
