# UniversalRecordEditor - Универсальный редактор записей

## Описание

`UniversalRecordEditor` - это универсальный компонент для редактирования записей всех коллекций PocketBase. Он объединяет функциональность ранее существовавших компонентов `RecordEditForm`, `QuickEditModal` и `CollectionManager`, предоставляя единый интерфейс для работы с данными.

## Основные возможности

- ✅ **Полная поддержка схемы PocketBase** - автоматическое чтение схемы из `pb_schema.json`
- ✅ **Все типы полей** - text, editor, file, relation, bool, number, date, email, url, json, select, autodate, password
- ✅ **Валидация данных** - проверка обязательных полей, форматов, ограничений
- ✅ **Связи между коллекциями** - поддержка relation полей с автозагрузкой данных
- ✅ **Множественные файлы** - поддержка загрузки нескольких файлов
- ✅ **Два режима отображения** - модальное окно и встроенная форма
- ✅ **Быстрое редактирование** - режим с ограниченным набором полей
- ✅ **Гибкая настройка** - возможность ограничить редактирование определенными полями

## Использование

### Базовое использование

```tsx
import { UniversalRecordEditor } from '@/components/admin/UniversalRecordEditor';

// Полное редактирование в модальном окне
<UniversalRecordEditor
  collection="doctors"
  recordId="abc123"
  mode="modal"
  isOpen={true}
  onClose={() => setIsOpen(false)}
  onSave={(record) => console.log('Сохранено:', record)}
/>

// Встроенная форма
<UniversalRecordEditor
  collection="services"
  recordId="def456"
  mode="inline"
  onSave={(record) => console.log('Сохранено:', record)}
/>
```

### Быстрое редактирование

```tsx
// Только основные поля для быстрого редактирования
<UniversalRecordEditor
  collection="doctors"
  recordId="abc123"
  mode="modal"
  showQuickFields={true}
  isOpen={true}
  onClose={() => setIsOpen(false)}
/>
```

### Ограничение полей

```tsx
// Редактирование только определенных полей
<UniversalRecordEditor
  collection="doctors"
  recordId="abc123"
  allowedFields={['name', 'position', 'experience']}
  mode="modal"
  isOpen={true}
  onClose={() => setIsOpen(false)}
/>
```

## Props

| Prop | Тип | Обязательный | Описание |
|------|-----|--------------|----------|
| `collection` | `string` | ✅ | Название коллекции PocketBase |
| `recordId` | `string` | ✅ | ID записи для редактирования |
| `initialRecord` | `Record<string, any>` | ❌ | Начальные данные записи |
| `mode` | `'modal' \| 'inline'` | ❌ | Режим отображения (по умолчанию: 'modal') |
| `title` | `string` | ❌ | Заголовок формы |
| `description` | `string` | ❌ | Описание формы |
| `showQuickFields` | `boolean` | ❌ | Показывать только основные поля (по умолчанию: false) |
| `allowedFields` | `string[]` | ❌ | Ограничить редактирование определенными полями |
| `onSave` | `(record: Record<string, any>) => void` | ❌ | Колбэк при сохранении |
| `onCancel` | `() => void` | ❌ | Колбэк при отмене |
| `onClose` | `() => void` | ❌ | Колбэк при закрытии |
| `pbUrl` | `string` | ❌ | URL PocketBase (по умолчанию: 'https://pb.stom-line.ru') |
| `token` | `string` | ❌ | Токен авторизации (по умолчанию: из localStorage) |
| `isOpen` | `boolean` | ❌ | Состояние модального окна (по умолчанию: true) |

## Поддерживаемые типы полей

### Текстовые поля
- `text` - обычное текстовое поле
- `email` - поле email с валидацией
- `url` - поле URL с валидацией
- `password` - поле пароля

### Числовые поля
- `number` - числовое поле с поддержкой min/max

### Даты
- `date` - поле даты с поддержкой min/max
- `autodate` - автоматические даты (только для чтения)

### Логические поля
- `bool` - чекбокс

### Контент
- `editor` - HTML редактор (TiptapEditor)
- `json` - JSON поле с валидацией

### Файлы
- `file` - загрузка файлов (поддержка множественных файлов)

### Связи
- `relation` - связи с другими коллекциями (одиночные и множественные)

### Выбор
- `select` - выпадающий список с предустановленными опциями

## Конфигурация быстрого редактирования

Для каждой коллекции определены поля для быстрого редактирования:

```typescript
const quickFieldsConfig = {
  doctors: ['name', 'surname', 'position', 'experience', 'short_description'],
  services: ['name', 'short_description', 'category'],
  reviews: ['author', 'title', 'content', 'date', 'is_published'],
  faq: ['question', 'answer', 'category', 'is_published'],
  news: ['title', 'slug', 'date', 'content', 'is_featured'],
  promos: ['title', 'subtitle', 'start_date', 'end_date', 'is_active'],
  pages: ['title', 'slug', 'content', 'is_published'],
  prices: ['name', 'price', 'category', 'service', 'is_active'],
  service_categories: ['name', 'slug', 'description'],
};
```

## Валидация

Компонент автоматически выполняет валидацию на основе схемы PocketBase:

- Проверка обязательных полей
- Валидация email и URL
- Проверка числовых ограничений (min/max)
- Валидация длины текста
- Проверка паттернов (regex)
- Валидация дат

## Интеграция с существующими компонентами

### CollectionManager
Автоматически использует `UniversalRecordEditor` для быстрого редактирования.

### EditButton
Поддерживает новый параметр `useModal` для использования модального редактора:

```tsx
<EditButton
  collection="doctors"
  id="abc123"
  useModal={true}
  showQuickFields={true}
/>
```

### AdminEditPage
Использует `UniversalRecordEditor` в inline режиме для полного редактирования.

## Миграция с старых компонентов

### Замена RecordEditForm

**Было:**
```tsx
<RecordEditForm
  collection="doctors"
  id="abc123"
  initialRecord={record}
  pbUrl="https://pb.stom-line.ru"
  token={token}
/>
```

**Стало:**
```tsx
<UniversalRecordEditor
  collection="doctors"
  recordId="abc123"
  initialRecord={record}
  mode="inline"
  pbUrl="https://pb.stom-line.ru"
  token={token}
/>
```

### Замена QuickEditModal

**Было:**
```tsx
<QuickEditModal
  isOpen={isOpen}
  onClose={onClose}
  collection="doctors"
  recordId="abc123"
  pbUrl="https://pb.stom-line.ru"
/>
```

**Стало:**
```tsx
<UniversalRecordEditor
  collection="doctors"
  recordId="abc123"
  mode="modal"
  showQuickFields={true}
  isOpen={isOpen}
  onClose={onClose}
  pbUrl="https://pb.stom-line.ru"
/>
```

## Тестирование

Для тестирования компонента доступна специальная страница:
`/admin/test-editor`

Она содержит тесты для всех коллекций в различных режимах работы.
