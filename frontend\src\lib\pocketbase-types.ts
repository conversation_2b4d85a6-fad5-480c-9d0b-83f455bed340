// Автоматически сгенерированные типы из схемы PocketBase
// Не редактируйте этот файл вручную!

export interface BaseRecord {
  id: string;
  created: string;
  updated: string;
}

// Интерфейс для коллекции doctors
export interface Doctors {
  surname: string;
  name: string;
  patronymic?: string;
  slug: string;
  position?: string;
  short_description?: string;
  biography?: string;
  photo?: string | string[];
  specializations?: string[];
  services?: string[];
  experience?: string;
  clinic?: string;
  meta_title?: string;
  meta_description?: string;
  is_featured?: boolean;
  sort_order?: number;
  certificates?: string;
  created?: string;
  updated?: string;
  expand?: {
    specializations?: ServiceCategories[];
    services?: Services[];
    certificates?: Files;
  };
}

// Интерфейс для коллекции faq
export interface Faq {
  question: string;
  answer?: string;
  category?: string;
  is_published?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

// Интерфейс для коллекции files
export interface Files extends BaseRecord {
  type?: 'certificate' | 'any' | 'image' | 'document';
  files?: string | string[];
  doctor?: string;
  name?: string;
  description?: string;
  expand?: {
    doctor?: Doctors;
  };
}

// Интерфейс для коллекции html_blocks
export interface HtmlBlocks {
  key: string;
  title?: string;
  description?: string;
  content?: string;
  section: 'hero' | 'features' | 'about' | 'contact' | 'general' | 'achievements_header' | 'achievements_stats' | 'achievements_about' | 'achievements_mission' | 'achievements_process' | 'achievements_team' | 'achievements_gallery' | 'achievements_cta';
  type: 'badge' | 'heading' | 'subheading' | 'paragraph' | 'button' | 'phone' | 'email' | 'address' | 'html' | 'statistic' | 'card_title' | 'card_description' | 'step_title' | 'step_description' | 'team_name' | 'team_position' | 'achievement_title' | 'achievement_description';
  is_active?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

// Интерфейс для коллекции news
export interface News {
  title: string;
  slug: string;
  date: string;
  content?: string;
  image?: string | string[];
  meta_title?: string;
  meta_description?: string;
  is_featured?: boolean;
  created?: string;
  updated?: string;
}

// Интерфейс для коллекции pages
export interface Pages {
  title: string;
  slug: string;
  content?: string;
  featured_image?: string | string[];
  gallery?: string | string[];
  parent_slug?: string;
  meta_title?: string;
  meta_description?: string;
  is_published?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

// Интерфейс для коллекции personal
export interface Personal {
  surname?: string;
  name?: string;
  patronymic?: string;
  about?: string;
  position?: string;
  created?: string;
  updated?: string;
}

// Интерфейс для коллекции prices
export interface Prices {
  name: string;
  category: string;
  service?: string;
  price: number;
  price_suffix?: string;
  price_prefix?: string;
  unit?: string;
  is_active?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
  expand?: {
    category?: ServiceCategories;
    service?: Services;
  };
}

// Интерфейс для коллекции promos
export interface Promos {
  title: string;
  subtitle?: string;
  slug: string;
  start_date?: string;
  end_date?: string;
  content?: string;
  image?: string | string[];
  related_services?: string[];
  meta_title?: string;
  meta_description?: string;
  is_active?: boolean;
  is_featured?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
  expand?: {
    related_services?: Services[];
  };
}

// Интерфейс для коллекции reviews
export interface Reviews {
  author: string;
  date?: string;
  title?: string;
  content?: string;
  image?: string | string[];
  is_published?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

// Интерфейс для коллекции service_categories
export interface ServiceCategories {
  name: string;
  slug: string;
  description?: string;
  image?: string | string[];
  meta_title?: string;
  meta_description?: string;
  created?: string;
  updated?: string;
}

// Интерфейс для коллекции services
export interface Services {
  name: string;
  slug: string;
  category: string;
  short_description?: string;
  content?: string;
  image?: string | string[];
  meta_title?: string;
  meta_description?: string;
  is_featured?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
  expand?: {
    category?: ServiceCategories;
  };
}

// Интерфейс для коллекции callback_requests
export interface CallbackRequests {
  name: string;
  phone: string;
  message?: string;
  status: 'new' | 'in_progress' | 'completed' | 'cancelled';
  source?: string;
  ip_address?: string;
  user_agent?: string;
  processed_at?: string;
  processed_by?: string;
  notes?: string;
  created?: string;
  updated?: string;
}

// Union тип для названий коллекций
export type CollectionName = 'callback_requests' | 'doctors' | 'faq' | 'files' | 'html_blocks' | 'news' | 'pages' | 'personal' | 'prices' | 'promos' | 'reviews' | 'service_categories' | 'services';

