#!/usr/bin/env node

/**
 * Скрипт для проверки маршрутов админ-панели
 * Проверяет соответствие маршрутов с коллекциями в схеме pb_schema.json
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Пути к файлам
const schemaPath = path.join(__dirname, '../public/pb_schema.json');
const adminPagesDir = path.join(__dirname, '../src/pages/admin');

// Считываем схему
const schema = JSON.parse(fs.readFileSync(schemaPath, 'utf8'));

// Получаем список коллекций из схемы
const schemaCollections = schema.map(collection => collection.name);

// Получаем список страниц админки
const adminPages = fs.readdirSync(adminPagesDir)
  .filter(file => file.endsWith('.astro'))
  .map(file => file.replace('.astro', ''));

// Исключаем системные страницы
const excludePages = ['index', 'login', 'clear-token', 'create', 'edit', 'test-auth', 'test-editor'];
const adminCollections = adminPages.filter(page => !excludePages.includes(page));

// Сравниваем
console.log('=== Проверка соответствия маршрутов админки ===\n');

console.log('📋 Коллекции по схеме:');
schemaCollections.forEach(collection => {
  console.log(`  ✅ ${collection}`);
});

console.log('\n📄 Страницы админки:');
adminCollections.forEach(page => {
  console.log(`  📄 ${page}`);
});

console.log('\n🔍 Анализ соответствия:');

const missingPages = [];
const extraPages = [];
const matchingPages = [];

schemaCollections.forEach(collection => {
  if (adminCollections.includes(collection)) {
    matchingPages.push(collection);
  } else {
    missingPages.push(collection);
  }
});

adminCollections.forEach(page => {
  if (!schemaCollections.includes(page)) {
    extraPages.push(page);
  }
});

console.log('\n✅ Совпадающие страницы:');
matchingPages.forEach(page => {
  console.log(`  ✅ ${page}`);
});

console.log('\n❌ Отсутствующие страницы:');
missingPages.forEach(page => {
  console.log(`  ❌ ${page} (нужна страница /admin/${page}.astro)`);
});

console.log('\n⚠️  Лишние страницы:');
extraPages.forEach(page => {
  console.log(`  ⚠️  ${page} (нет соответствующей коллекции в схеме)`);
});

console.log('\n=== Результат ===');
if (missingPages.length === 0 && extraPages.length === 0) {
  console.log('🎉 Все маршруты админки соответствуют схеме!');
} else {
  console.log(`⚠️  Найдено проблем: ${missingPages.length + extraPages.length}`);
  console.log(`   - Отсутствующих страниц: ${missingPages.length}`);
  console.log(`   - Лишних страниц: ${extraPages.length}`);
}

// Специальная проверка для документов
console.log('\n📁 Проверка документов:');
const hasFilesCollection = schemaCollections.includes('files');
const hasDocumentsPage = adminCollections.includes('documents');

if (hasFilesCollection && hasDocumentsPage) {
  console.log('  ✅ Коллекция "files" имеет соответствующую страницу "documents"');
} else if (!hasFilesCollection) {
  console.log('  ❌ Коллекция "files" отсутствует в схеме');
} else if (!hasDocumentsPage) {
  console.log('  ❌ Страница "documents" отсутствует');
}

console.log('\n📊 Статистика:');
console.log(`  Коллекций в схеме: ${schemaCollections.length}`);
console.log(`  Страниц админки: ${adminCollections.length}`);
console.log(`  Совпадающих: ${matchingPages.length}`);
console.log(`  Отсутствующих: ${missingPages.length}`);
console.log(`  Лишних: ${extraPages.length}`);
