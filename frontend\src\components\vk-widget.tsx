'use client'

import { useEffect, useRef } from 'react'
import { motion, useInView } from 'framer-motion'

declare global {
  interface Window {
    VK: {
      Widgets: {
        Group: (elementId: string, options: any, groupId: number) => void
      }
    }
  }
}

export default function VKWidget() {
  const containerRef = useRef<HTMLDivElement>(null)
  const widgetRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: true, margin: '-100px 0px' })

  useEffect(() => {
    if (!isInView || !widgetRef.current) return

    // Проверяем, что VK API загружен
    const initWidget = () => {
      if (window.VK && window.VK.Widgets) {
        // Очищаем контейнер перед инициализацией
        if (widgetRef.current) {
          widgetRef.current.innerHTML = ''
        }

        // Инициализируем виджет с корпоративными цветами
        window.VK.Widgets.Group(
          'vk_groups',
          {
            mode: 4, // Режим отображения (4 - список участников)
            wide: 1, // Широкий режим
            height: 700, // Высота виджета
            width: 'auto',
            color1: 'FFFFFF', // Цвет фона
            color2: '000000', // Цвет текста
            color3: '8BC34A' // Корпоративный цвет (основной зеленый),
          },
          117305081
        ) // ID группы ВК

        const observer = new MutationObserver(() => {
          const wallPosts = window.document.querySelector('#page_wall_posts')
          if (wallPosts) {
            console.log("🚀 ~ observer ~ wallPosts:", wallPosts)
            wallPosts.classList.add('text-lg') // Добавляем класс для большого шрифта
            observer.disconnect()
          }
        })

        // Начинаем наблюдение за изменениями в виджете
        if (widgetRef.current) {
          observer.observe(widgetRef.current, {
            childList: true,
            subtree: true
          })
        }
      }
    }

    // Если VK API уже загружен, инициализируем сразу
    if (window.VK) {
      initWidget()
    } else {
      // Иначе ждем загрузки
      const checkVK = setInterval(() => {
        if (window.VK) {
          clearInterval(checkVK)
          initWidget()
        }
      }, 100)

      // Очищаем интервал через 10 секунд, если API не загрузился
      // setTimeout(() => clearInterval(checkVK), 10000)
    }
    if (isInView) {
      console.log('ddd:', window.document.querySelector('#page_wall_posts'))
    }
  }, [isInView])

  return (
    <section className='relative py-16 md:py-24'>
      {/* Фоновые элементы */}
      <div className='from-olive-50/80 to-olive-100/60 absolute inset-0 bg-gradient-to-br via-white'></div>
      <div className='absolute inset-0 bg-center opacity-[0.03]'></div>

      {/* Декоративные элементы */}
      <div className='pointer-events-none absolute inset-0 overflow-hidden'>
        <div className='bg-olive-400/20 absolute top-[20%] -left-[5%] h-[200px] w-[200px] rounded-full blur-[80px]' />
        <div className='bg-olive-500/20 absolute -right-[5%] bottom-[30%] h-[150px] w-[150px] rounded-full blur-[60px]' />
      </div>

      <div ref={containerRef} className='relative z-10 container mx-auto px-4 md:px-6'>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
          className='mx-auto w-[360px] text-center sm:w-[800px]'
        >
          {/* Заголовок секции */}
          <div className='mb-8'>
            <div className='from-olive-100/80 to-olive-200/80 text-olive-700 border-olive-200/50 inline-flex items-center rounded-full border bg-gradient-to-r px-4 py-2 text-sm font-medium shadow-lg backdrop-blur-sm'>
              <span className='from-olive-500 to-olive-600 mr-2 block h-2 w-2 rounded-full bg-gradient-to-r'></span>
              Мы в социальных сетях
            </div>
            <h2 className='text-olive-900 mt-4 text-3xl font-bold md:text-4xl'>
              Присоединяйтесь к нашему <span className='from-olive-600 to-olive-500 bg-gradient-to-r bg-clip-text text-transparent'>сообществу</span>
            </h2>
            <p className='text-olive-700 mx-auto mt-4 max-w-2xl'>
              Следите за новостями клиники, полезными советами по уходу за зубами и специальными предложениями в нашей группе ВКонтакте
            </p>
          </div>

          {/* Контейнер для VK виджета */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className='to-olive-50/90 border-olive-200/50 overflow-hidden rounded-2xl border bg-gradient-to-br from-white/90 p-1 shadow-2xl backdrop-blur-sm'
          >
            <div className='rounded-xl bg-white/80 p-6'>
              {/* VK виджет */}
              <div id='vk_groups' ref={widgetRef} className='flex rounded-lg'>
                {/* Загрузочный индикатор */}
                <div className='flex h-96 items-center justify-center'>
                  <div className='flex flex-col items-center space-y-4'>
                    <div className='border-olive-200 border-t-olive-500 h-12 w-12 animate-spin rounded-full border-4'></div>
                    <p className='text-olive-600 font-medium'>Загрузка сообщества...</p>
                  </div>
                </div>
              </div>

              {/* Дополнительная информация */}
              <div className='border-olive-200/50 mt-6 border-t pt-6'>
                <p className='text-olive-600 mb-4 text-sm'>Не можете увидеть виджет? Посетите нашу группу напрямую:</p>
                <a
                  href='https://vk.com/club117305081'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 inline-flex items-center rounded-lg bg-gradient-to-r px-6 py-3 font-medium text-white shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl'
                >
                  <svg className='mr-2 h-5 w-5' fill='currentColor' viewBox='0 0 24 24'>
                    <path d='M15.684 0H8.316C1.592 0 0 1.592 0 8.316v7.368C0 22.408 1.592 24 8.316 24h7.368C22.408 24 24 22.408 24 15.684V8.316C24 1.592 22.408 0 15.684 0zm3.692 17.123h-1.744c-.66 0-.864-.525-2.05-1.727-1.033-1.01-1.49-.9-1.744-.9-.356 0-.458.102-.458.593v1.575c0 .424-.135.678-1.253.678-1.846 0-3.896-1.118-5.335-3.202C4.624 10.857 4.03 8.57 4.03 8.096c0-.254.102-.491.593-.491h1.744c.441 0 .61.203.78.678.863 2.49 2.303 4.675 2.896 4.675.22 0 .322-.102.322-.66V9.721c-.068-1.186-.695-1.287-.695-1.71 0-.204.169-.407.441-.407h2.744c.373 0 .508.203.508.643v3.473c0 .373.169.508.271.508.22 0 .407-.135.813-.542 1.254-1.406 2.151-3.574 2.151-3.574.119-.254.322-.491.763-.491h1.744c.525 0 .644.271.525.643-.254 1.151-2.456 4.054-2.456 4.054-.203.339-.271.491 0 .847.203.254.864.847 1.305 1.364.745.847 1.32 1.558 1.473 2.05.169.491-.085.744-.576.744z' />
                  </svg>
                  Перейти в группу ВКонтакте
                </a>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
