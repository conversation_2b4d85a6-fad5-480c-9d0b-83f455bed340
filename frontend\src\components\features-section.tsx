"use client"

import { motion, useInView } from "framer-motion"
import { useRef } from "react"
import { Users, Award, Heart, Clock } from "lucide-react"

export default function FeaturesSection() {
  const containerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: true, margin: "-100px 0px" })

  return (
    <section id="о нас" className="relative py-20 md:py-32 overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#8BC34A]/5 via-white to-[#8BC34A]/5"></div>
      <div className="absolute inset-0 bg-center opacity-[0.05]"></div>

      <div className="container relative z-10 mx-auto px-4 md:px-6">
        <motion.div
          ref={containerRef}
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Заголовок */}
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6 }}
              className="inline-flex items-center rounded-full bg-gradient-to-r from-[#8BC34A]/20 to-[#85C026]/20 px-4 py-2 text-sm font-medium text-gray-800 backdrop-blur-sm shadow-lg border border-[#8BC34A]/30 mb-8"
            >
              <span className="mr-2 block h-2 w-2 rounded-full bg-gradient-to-r from-[#8BC34A] to-[#4E8C29]"></span>
              Почему выбирают нас
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 via-[#4E8C29] to-gray-900 bg-clip-text text-transparent mb-6"
            >
              Почему выбирают
              <br />
              <span className="text-gray-800">нашу клинику?</span>
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed"
            >
              Мы объединили многолетний опыт, современные технологии
              и индивидуальный подход для создания идеальной улыбки
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {/* Преимущество 1 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-[#8BC34A]/20 p-1 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="rounded-lg bg-white/60 p-6">
                <div className="mb-4 flex items-center justify-center">
                  <div className="bg-[#8BC34A]/20 p-3 rounded-full group-hover:bg-[#8BC34A]/30 transition-colors duration-300">
                    <Users className="h-6 w-6 text-[#8BC34A]" />
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 text-center group-hover:text-[#8BC34A] transition-colors duration-300">
                  Опытная команда
                </h3>
                <p className="text-gray-600 text-sm text-center leading-relaxed">
                  Врачи с опытом работы более 15 лет и постоянным повышением квалификации
                </p>
              </div>
            </motion.div>

            {/* Преимущество 2 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-[#8BC34A]/20 p-1 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="rounded-lg bg-white/60 p-6">
                <div className="mb-4 flex items-center justify-center">
                  <div className="bg-[#8BC34A]/20 p-3 rounded-full group-hover:bg-[#8BC34A]/30 transition-colors duration-300">
                    <Award className="h-6 w-6 text-[#8BC34A]" />
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 text-center group-hover:text-[#8BC34A] transition-colors duration-300">
                  Современное оборудование
                </h3>
                <p className="text-gray-600 text-sm text-center leading-relaxed">
                  Новейшие технологии диагностики и лечения последнего поколения
                </p>
              </div>
            </motion.div>

            {/* Преимущество 3 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-[#8BC34A]/20 p-1 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="rounded-lg bg-white/60 p-6">
                <div className="mb-4 flex items-center justify-center">
                  <div className="bg-[#8BC34A]/20 p-3 rounded-full group-hover:bg-[#8BC34A]/30 transition-colors duration-300">
                    <Heart className="h-6 w-6 text-[#8BC34A]" />
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 text-center group-hover:text-[#8BC34A] transition-colors duration-300">
                  Комфорт пациентов
                </h3>
                <p className="text-gray-600 text-sm text-center leading-relaxed">
                  Уютная атмосфера, современная стоматология без боли и стресса
                </p>
              </div>
            </motion.div>

            {/* Преимущество 4 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.6, delay: 0.9 }}
              className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-[#8BC34A]/20 p-1 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="rounded-lg bg-white/60 p-6">
                <div className="mb-4 flex items-center justify-center">
                  <div className="bg-[#8BC34A]/20 p-3 rounded-full group-hover:bg-[#8BC34A]/30 transition-colors duration-300">
                    <Clock className="h-6 w-6 text-[#8BC34A]" />
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 text-center group-hover:text-[#8BC34A] transition-colors duration-300">
                  Быстрое обслуживание
                </h3>
                <p className="text-gray-600 text-sm text-center leading-relaxed">
                  Минимальное время ожидания и эффективная работа
                </p>
              </div>
            </motion.div>
          </div>

          {/* Статистика */}
          {/* <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.6, delay: 1.0 }}
            className="hidden sm:block relative overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-[#8BC34A]/20 p-1 backdrop-blur-sm shadow-lg"
          >
            <div className="rounded-lg bg-white/60 sm:p-8 p-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 1.2 }}
                className="text-center mb-8"
              >
                <h3 className="text-2xl font-bold text-gray-800 mb-2">
                  Наши достижения
                </h3>
                <div className="w-16 h-1 bg-[#8BC34A] mx-auto rounded-full"></div>
              </motion.div>

              <div className="grid grid-cols-2 md:grid-cols-4 sm:gap-6 gap-2">
                {[
                  { value: "98%", label: "Довольных пациентов", delay: 1.4 },
                  { value: "Профессиональная", label: "помощь", delay: 1.5 },
                  { value: "15+", label: "Лет опыта", delay: 1.6 },
                  { value: "5000+", label: "Успешных операций", delay: 1.7 }
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.6, delay: stat.delay }}
                    className="text-center group"
                  >
                    <div className="text-lg sm:text-3xl font-bold text-[#8BC34A] mb-2 group-hover:scale-110 transition-transform duration-300">
                      {stat.value}
                    </div>
                    <div className="text-sm font-medium text-gray-600 group-hover:text-gray-800 transition-colors duration-300">
                      {stat.label}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div> */}
        </motion.div>
      </div>
    </section>
  )
}

