#!/usr/bin/env node

/**
 * Скрипт для импорта коллекции callback_requests в PocketBase
 * и добавления тестовых данных
 */

const fs = require('fs');
const path = require('path');

// Для Node.js < 18 может потребоваться установить node-fetch
// const fetch = require('node-fetch');

// Для Node.js 18+ fetch доступен глобально
if (typeof fetch === 'undefined') {
  global.fetch = require('node-fetch');
}

// Конфигурация
const POCKETBASE_URL = process.env.POCKETBASE_URL || 'https://pb.stom-line.ru';
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || '!10Havafi1';

// Пути к файлам
const COLLECTION_FILE = path.join(__dirname, 'callback_requests_collection.json');
const SAMPLE_DATA_FILE = path.join(__dirname, 'callback_requests_sample_data.json');

/**
 * Функция для выполнения HTTP запросов
 */
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${data.message || 'Unknown error'}`);
    }
    
    return data;
  } catch (error) {
    console.error(`Ошибка запроса к ${url}:`, error.message);
    throw error;
  }
}

/**
 * Авторизация администратора
 */
async function authenticateAdmin() {
  console.log('🔐 Авторизация администратора...');
  
  const authData = await makeRequest(`${POCKETBASE_URL}/api/admins/auth-with-password`, {
    method: 'POST',
    body: JSON.stringify({
      identity: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    })
  });
  
  console.log('✅ Авторизация успешна');
  return authData.token;
}

/**
 * Проверка существования коллекции
 */
async function checkCollectionExists(token, collectionName) {
  try {
    await makeRequest(`${POCKETBASE_URL}/api/collections/${collectionName}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return true;
  } catch (error) {
    if (error.message.includes('404')) {
      return false;
    }
    throw error;
  }
}

/**
 * Создание коллекции
 */
async function createCollection(token) {
  console.log('📦 Создание коллекции callback_requests...');
  
  // Читаем схему коллекции
  const collectionSchema = JSON.parse(fs.readFileSync(COLLECTION_FILE, 'utf8'));
  
  const collection = await makeRequest(`${POCKETBASE_URL}/api/collections`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(collectionSchema[0])
  });
  
  console.log('✅ Коллекция создана:', collection.name);
  return collection;
}

/**
 * Добавление тестовых данных
 */
async function addSampleData(token) {
  console.log('📝 Добавление тестовых данных...');
  
  // Читаем тестовые данные
  const sampleData = JSON.parse(fs.readFileSync(SAMPLE_DATA_FILE, 'utf8'));
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const [index, record] of sampleData.entries()) {
    try {
      await makeRequest(`${POCKETBASE_URL}/api/collections/callback_requests/records`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(record)
      });
      
      successCount++;
      console.log(`  ✅ Запись ${index + 1}/${sampleData.length} добавлена`);
    } catch (error) {
      errorCount++;
      console.error(`  ❌ Ошибка при добавлении записи ${index + 1}:`, error.message);
    }
  }
  
  console.log(`📊 Результат: ${successCount} успешно, ${errorCount} ошибок`);
}

/**
 * Основная функция
 */
async function main() {
  try {
    console.log('🚀 Начинаем импорт коллекции callback_requests в PocketBase');
    console.log(`📍 URL PocketBase: ${POCKETBASE_URL}`);
    
    // Проверяем наличие файлов
    if (!fs.existsSync(COLLECTION_FILE)) {
      throw new Error(`Файл схемы коллекции не найден: ${COLLECTION_FILE}`);
    }
    
    if (!fs.existsSync(SAMPLE_DATA_FILE)) {
      console.log('⚠️  Файл с тестовыми данными не найден, пропускаем добавление данных');
    }
    
    // Авторизуемся
    const token = await authenticateAdmin();
    
    // Проверяем, существует ли коллекция
    const collectionExists = await checkCollectionExists(token, 'callback_requests');
    
    if (collectionExists) {
      console.log('ℹ️  Коллекция callback_requests уже существует');
      
      const answer = await new Promise((resolve) => {
        const readline = require('readline').createInterface({
          input: process.stdin,
          output: process.stdout
        });
        
        readline.question('Хотите добавить только тестовые данные? (y/n): ', (answer) => {
          readline.close();
          resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
        });
      });
      
      if (!answer) {
        console.log('❌ Импорт отменен');
        return;
      }
    } else {
      // Создаем коллекцию
      await createCollection(token);
    }
    
    // Добавляем тестовые данные, если файл существует
    if (fs.existsSync(SAMPLE_DATA_FILE)) {
      await addSampleData(token);
    }
    
    console.log('🎉 Импорт успешно завершен!');
    console.log('');
    console.log('📋 Следующие шаги:');
    console.log('1. Откройте админ-панель PocketBase');
    console.log('2. Перейдите в коллекцию "callback_requests"');
    console.log('3. Проверьте структуру и данные');
    console.log('4. Обновите API в frontend/src/pages/api/callback.ts');
    
  } catch (error) {
    console.error('❌ Ошибка при импорте:', error.message);
    process.exit(1);
  }
}

// Запускаем скрипт, если он вызван напрямую
if (require.main === module) {
  main();
}

module.exports = {
  main,
  authenticateAdmin,
  createCollection,
  addSampleData
};
