---
import Layout from '@/layouts/Layout.astro'
import { AccessibilityInfo } from '@/components/accessibility/AccessibilityInfo'
---

<Layout title="Версия для слабовидящих - STOM-LINE" description="Информация о версии сайта для слабовидящих. Соответствие ГОСТ Р 52872-2019 и WCAG 2.1 AA.">
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-center mb-8 text-olive-800">
        Версия для слабовидящих
      </h1>
      
      <AccessibilityInfo client:load />
      
      <!-- Дополнительная информация -->
      <div class="mt-8 prose prose-lg max-w-none">
        <h2 class="text-2xl font-semibold mb-4">О версии для слабовидящих</h2>
        
        <p class="mb-4">
          Стоматологическая клиника STOM-LINE заботится о доступности своего сайта для всех пользователей, 
          включая людей с нарушениями зрения. Наша версия для слабовидящих разработана в соответствии с 
          российскими и международными стандартами доступности.
        </p>

        <h3 class="text-xl font-semibold mb-3">Основные возможности</h3>
        <ul class="list-disc pl-6 mb-6 space-y-2">
          <li><strong>Увеличенный шрифт:</strong> Минимальный размер шрифта 18px с возможностью увеличения до 24px</li>
          <li><strong>Высокий контраст:</strong> Черный текст на белом фоне или инвертированные цвета</li>
          <li><strong>Упрощенный дизайн:</strong> Убраны декоративные элементы, анимации и градиенты</li>
          <li><strong>Четкие границы:</strong> Все интерактивные элементы имеют четкие границы</li>
          <li><strong>Увеличенные кнопки:</strong> Минимальный размер интерактивных элементов 48x48px</li>
          <li><strong>Навигация с клавиатуры:</strong> Полная поддержка навигации без мыши</li>
          <li><strong>Поддержка скринридеров:</strong> Семантическая разметка и ARIA-атрибуты</li>
        </ul>

        <h3 class="text-xl font-semibold mb-3">Соответствие стандартам</h3>
        <p class="mb-4">
          Версия для слабовидящих соответствует следующим стандартам:
        </p>
        <ul class="list-disc pl-6 mb-6 space-y-2">
          <li><strong>ГОСТ Р 52872-2019</strong> - Интернет-ресурсы и мобильные приложения. Требования доступности для инвалидов по зрению</li>
          <li><strong>WCAG 2.1 AA</strong> - Руководство по обеспечению доступности веб-контента</li>
          <li><strong>Приказ Минкомсвязи России №467</strong> - Требования к сайтам государственных органов</li>
          <li><strong>Федеральный закон №181-ФЗ</strong> - О социальной защите инвалидов в Российской Федерации</li>
        </ul>

        <h3 class="text-xl font-semibold mb-3">Как включить версию для слабовидящих</h3>
        <ol class="list-decimal pl-6 mb-6 space-y-2">
          <li>Нажмите кнопку "Для слабовидящих" в верхней части любой страницы сайта</li>
          <li>Или используйте горячую клавишу <kbd class="px-2 py-1 bg-gray-100 rounded">Alt + A</kbd></li>
          <li>Настройте размер шрифта и контрастность в меню настроек</li>
          <li>Ваши настройки сохранятся для следующих посещений</li>
        </ol>

        <h3 class="text-xl font-semibold mb-3">Обратная связь</h3>
        <p class="mb-4">
          Мы постоянно работаем над улучшением доступности нашего сайта. Если у вас есть предложения 
          или вы столкнулись с проблемами при использовании версии для слабовидящих, пожалуйста, 
          свяжитесь с нами:
        </p>
        <div class="bg-olive-50 p-4 rounded-lg">
          <p><strong>Email:</strong> <EMAIL></p>
          <p><strong>Телефон:</strong> +7 (XXX) XXX-XX-XX</p>
          <p><strong>Время работы:</strong> Понедельник - Пятница, 9:00 - 18:00</p>
        </div>
      </div>
    </div>
  </div>
</Layout>

<style>
  /* Дополнительные стили для страницы доступности */
  kbd {
    font-family: monospace;
    font-size: 0.875em;
  }
  
  .prose h2, .prose h3 {
    color: #4D8C29;
  }
  
  .prose ul, .prose ol {
    margin-bottom: 1.5rem;
  }
  
  .prose li {
    margin-bottom: 0.5rem;
  }
</style>
