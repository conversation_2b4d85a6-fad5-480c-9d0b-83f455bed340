/**
 * Тесты для новых коллекций html_blocks и personal
 */

import { describe, it, expect } from 'vitest';

describe('New Collections Integration', () => {
  it('должен включать новые коллекции в список', () => {
    const collections = ['doctors', 'services', 'promos', 'reviews', 'faq', 'news', 'pages', 'prices', 'html_blocks', 'personal'];

    // Проверяем, что новые коллекции включены в список
    expect(collections).toContain('html_blocks');
    expect(collections).toContain('personal');
    expect(collections).toHaveLength(10);
  });

  it('должен корректно обрабатывать типы полей новых коллекций', () => {
    // HTML Blocks - только editor поле
    const htmlBlocksFields = ['content'];
    expect(htmlBlocksFields).toContain('content');
    expect(htmlBlocksFields).toHaveLength(1);

    // Personal - текстовые поля и editor
    const personalFields = ['surname', 'name', 'patronymic', 'position', 'about'];
    expect(personalFields).toContain('surname');
    expect(personalFields).toContain('name');
    expect(personalFields).toContain('about'); // editor поле
    expect(personalFields).toHaveLength(5);
  });
});

describe('Collection Configuration', () => {
  it('должен правильно настроить отображаемые поля для html_blocks', () => {
    const htmlBlocksDisplayFields = ['content'];
    expect(htmlBlocksDisplayFields).toEqual(['content']);
  });

  it('должен правильно настроить отображаемые поля для personal', () => {
    const personalDisplayFields = ['surname', 'name', 'position'];
    expect(personalDisplayFields).toEqual(['surname', 'name', 'position']);
  });

  it('должен правильно настроить поля для быстрого редактирования html_blocks', () => {
    const htmlBlocksQuickFields = ['content'];
    expect(htmlBlocksQuickFields).toEqual(['content']);
  });

  it('должен правильно настроить поля для быстрого редактирования personal', () => {
    const personalQuickFields = ['surname', 'name', 'patronymic', 'position', 'about'];
    expect(personalQuickFields).toEqual(['surname', 'name', 'patronymic', 'position', 'about']);
  });
});
