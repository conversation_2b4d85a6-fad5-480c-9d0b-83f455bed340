import pb from './pocketbase';

// Интерфейс для связанных данных
export interface RelatedRecord {
  id: string;
  name?: string;
  title?: string;
  description?: string;
  collectionId?: string;
  collectionName?: string;
  [key: string]: any;
}

// Интерфейс для формы обратной связи (соответствует схеме PocketBase callback_requests)
export interface CallbackFormData {
  name: string;
  phone: string;
  message?: string;
}

// Интерфейс для записи callback_requests в PocketBase
export interface CallbackRequest {
  id: string;
  name: string;
  phone: string;
  message?: string;
  ip_address?: string;
  isProcessed: boolean;
  created: string;
  updated: string;
}

// Интерфейс для ответа API
export interface CallbackApiResponse {
  success: boolean;
  message: string;
  id?: string;
}

// Интерфейс для ошибок валидации
export interface ValidationErrors {
  name?: string;
  phone?: string;
  message?: string;
}

// Интерфейс для специалиста из PocketBase
export interface Doctor {
  id: string
  surname: string
  name: string
  patronymic?: string
  position: string
  photo?: string | { name?: string; filename?: string; [key: string]: any }
  experience?: string
  short_description?: string
  biography?: string

  // Связи с другими коллекциями
  specializations?: string | string[]
  services?: string | string[]
  certificates?: unknown

  // Расширенные данные для связей
  expand?: {
    specializations?: RelatedRecord[]
    services?: RelatedRecord[]
    certificates?: RelatedRecord[]
  }
}



// Интерфейс для категории услуг
export interface ServiceCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  meta_title?: string;
  meta_description?: string;
  created?: string;
  updated?: string;
}

// Интерфейс для услуги из PocketBase
export interface Service {
  id: string;
  name: string;
  slug: string;
  category: string;
  short_description?: string;
  content?: string;
  image?: string;
  meta_title?: string;
  meta_description?: string;
  is_featured?: boolean;
  sort_order?: number;
  expand?: {
    category?: ServiceCategory;
    doctors?: Doctor[];
  };
  created?: string;
  updated?: string;
}

// Интерфейс для FAQ из PocketBase
export interface FAQ {
  id: string;
  question: string;
  answer?: string;
  category?: string;
  is_published?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

// Интерфейс для отзывов из PocketBase
export interface Review {
  id: string;
  author: string;
  date?: string;
  title?: string;
  content?: string;
  image?: string;
  is_published?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

// Интерфейс для акций из PocketBase
export interface Promo {
  id: string;
  title: string;
  subtitle?: string;
  slug: string;
  start_date?: string;
  end_date?: string;
  content?: string;
  image?: string;
  related_services?: string | string[];
  meta_title?: string;
  meta_description?: string;
  is_active?: boolean;
  is_featured?: boolean;
  sort_order?: number;
  expand?: {
    related_services?: Service[];
  };
  created?: string;
  updated?: string;
}

// Интерфейс для новости из PocketBase
export interface News {
  id: string;
  title: string;
  slug: string;
  date: string;
  content?: string;
  image?: string | { name?: string; filename?: string; [key: string]: any };
  meta_title?: string;
  meta_description?: string;
  is_featured?: boolean;
  created?: string;
  updated?: string;
}

// Экспортируем типы из автоматически сгенерированного файла
export type { HtmlBlocks, Personal } from './pocketbase-types';

// Интерфейс для страницы из PocketBase
export interface Page {
  id: string;
  title: string;
  slug: string;
  content?: string;
  featured_image?: string | { name?: string; filename?: string; [key: string]: any };
  gallery?: string | string[];
  parent_slug?: string;
  meta_title?: string;
  meta_description?: string;
  is_published?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

/**
 * Получить список специалистов из PocketBase
 */
export async function getSpecialists(): Promise<Doctor[]> {
  try {
    console.log('Запрос специалистов из PocketBase...');

    // Используем метод getFullList для получения всех записей
    const records = await pb.collection('doctors').getFullList({
      sort: 'sort_order',
      expand: 'specializations,services,certificates',
      requestKey: null, // Отключаем дедупликацию запросов
    });

    console.log('Получено специалистов из PocketBase:', records.length);
    return records as unknown as Doctor[];
  } catch (error) {
    console.error('Ошибка при получении специалистов из PocketBase:', error);

    // Попробуем альтернативный запрос без expand
    try {
      console.log('Повторный запрос без expand...');
      const records = await pb.collection('doctors').getFullList({
        sort: 'sort_order',
        requestKey: null,
      });

      console.log('Получено специалистов (без expand):', records.length);
      return records as unknown as Doctor[];
    } catch (retryError) {
      console.error('Повторная ошибка при получении специалистов:', retryError);
      return [];
    }
  }
}

/**
 * Получить услугу по ID
 */
export async function getServiceById(id: string): Promise<Service | null> {
  try {
    const record = await pb.collection('services').getOne(id, {
      expand: 'category'
    });
    return record as unknown as Service;
  } catch (error) {
    console.error(`Ошибка при получении услуги с ID ${id}:`, error);
    return null;
  }
}

/**
 * Получить список категорий услуг
 */
export async function getServiceCategories(): Promise<ServiceCategory[]> {
  try {
    const records = await pb.collection('service_categories').getFullList({
      sort: 'name',
      requestKey: null,
    });

    console.log('Получено категорий услуг из PocketBase:', records.length);

    return records as unknown as ServiceCategory[];
  } catch (error) {
    console.error('Ошибка при получении категорий услуг из PocketBase:', error);
    return [];
  }
}

/**
 * Получить список услуг
 */
export async function getServices(filter?: string): Promise<Service[]> {
  try {
    // Сначала получаем услуги с expand только для категории
    const services = await pb.collection('services').getFullList({
      sort: 'sort_order',
      expand: 'category',
      filter: filter,
      requestKey: null,
    });

    // Затем получаем всех врачей с их услугами
    let doctors: any[] = [];
    try {
      doctors = await pb.collection('doctors').getFullList({
        expand: 'services',
        requestKey: null,
      });
    } catch (doctorsError) {
      console.warn('Не удалось загрузить врачей:', doctorsError);
    }

    // Создаем карту услуг для быстрого поиска
    const servicesMap = new Map();
    services.forEach(service => {
      servicesMap.set(service.id, service);
    });

    // Добавляем врачей к каждой услуге
    const servicesWithDoctors = services.map(service => {
      const relatedDoctors = doctors.filter(doctor => {
        const doctorServices = doctor.expand?.services;
        if (!doctorServices) return false;

        // Проверяем, является ли services массивом
        if (Array.isArray(doctorServices)) {
          return doctorServices.some((s: any) => s.id === service.id);
        }

        // Если services - это строка с ID услуг, разделенных запятыми
        if (typeof doctorServices === 'string') {
          return doctorServices.split(',').includes(service.id);
        }

        // Если services - это объект с одной услугой
        if (typeof doctorServices === 'object' && doctorServices.id) {
          return doctorServices.id === service.id;
        }

        return false;
      });

      return {
        ...service,
        expand: {
          ...service.expand,
          doctors: relatedDoctors.length > 0 ? relatedDoctors : undefined
        }
      };
    });

    console.log('Получено услуг из PocketBase:', servicesWithDoctors.length);
    if (doctors.length > 0) {
      console.log('Получено врачей из PocketBase:', doctors.length);

      // Логируем информацию о врачах для каждой услуги
      servicesWithDoctors.forEach((service, index) => {
        if (service.expand?.doctors && service.expand.doctors.length > 0) {
          console.log(`Услуга ${index + 1} (${service.name}): найдено ${service.expand.doctors.length} врачей`);
        }
      });
    }

    return servicesWithDoctors as unknown as Service[];
  } catch (error) {
    console.error('Ошибка при получении услуг из PocketBase:', error);
    return [];
  }
}

/**
 * Получить список часто задаваемых вопросов
 */
export async function getFAQs(filter?: string) {
  try {
    const records = await pb.collection('faq').getFullList({
      sort: 'sort_order',
      filter: filter || 'is_published = true'
    });

    console.log('Получено FAQ из PocketBase:', records.length);

    return records as unknown as FAQ[];
  } catch (error) {
    console.error('Ошибка при получении FAQ из PocketBase:', error);
    return [];
  }
}

/**
 * Получить список отзывов
 */
export async function getReviews(filter?: string) {
  try {
    const records = await pb.collection('reviews').getFullList({
      sort: 'sort_order',
      filter: filter || 'is_published = true'
    });

    console.log('Получено отзывов из PocketBase:', records.length);

    return records as unknown as Review[];
  } catch (error) {
    console.error('Ошибка при получении отзывов из PocketBase:', error);
    return [];
  }
}

/**
 * Получить список акций
 */
export async function getPromos(filter?: string) {
  try {
    const records = await pb.collection('promos').getFullList({
      sort: 'sort_order',
      expand: 'related_services',
      filter: filter || 'is_active = true'
    });

    console.log('Получено акций из PocketBase:', records.length);

    return records as unknown as Promo[];
  } catch (error) {
    console.error('Ошибка при получении акций из PocketBase:', error);
    return [];
  }
}

/**
 * Получить акцию для поп-апа
 */
export async function getPopupPromo(): Promise<Promo | null> {
  try {
    const record = await pb.collection('promos').getFirstListItem('slug="popup-promo" && is_active=true');

    console.log('Получена акция для поп-апа:', record.title);

    return record as unknown as Promo;
  } catch (error) {
    console.log('Акция для поп-апа не найдена или неактивна');
    return null;
  }
}

/**
 * Получить список новостей
 */
export async function getNews(filter?: string) {
  try {
    const records = await pb.collection('news').getFullList({
      sort: '-date',
      filter: filter
    });

    console.log('Получено новостей из PocketBase:', records.length);

    return records as unknown as News[];
  } catch (error) {
    console.error('Ошибка при получении новостей из PocketBase:', error);
    return [];
  }
}

/**
 * Получить новость по ID
 */
export async function getNewsById(id: string): Promise<News | null> {
  try {
    const record = await pb.collection('news').getOne(id);
    return record as unknown as News;
  } catch (error) {
    console.error(`Ошибка при получении новости с ID ${id}:`, error);
    return null;
  }
}

/**
 * Получить список страниц
 */
export async function getPages(filter?: string) {
  try {
    const records = await pb.collection('pages').getFullList({
      sort: 'sort_order',
      filter: filter || 'is_published = true'
    });

    console.log('Получено страниц из PocketBase:', records.length);

    return records as unknown as Page[];
  } catch (error) {
    console.error('Ошибка при получении страниц из PocketBase:', error);
    return [];
  }
}

/**
 * Получить страницу по ID
 */
export async function getPageById(id: string): Promise<Page | null> {
  try {
    const record = await pb.collection('pages').getOne(id);
    return record as unknown as Page;
  } catch (error) {
    console.error(`Ошибка при получении страницы с ID ${id}:`, error);
    return null;
  }
}

/**
 * Получить страницу по slug
 */
export async function getPageBySlug(slug: string): Promise<Page | null> {
  try {
    const record = await pb.collection('pages').getFirstListItem(`slug="${slug}"`);
    return record as unknown as Page;
  } catch (error) {
    console.error(`Ошибка при получении страницы с slug ${slug}:`, error);
    return null;
  }
}

/**
 * Получить HTML блоки
 */
export async function getHtmlBlocks(section?: string): Promise<HtmlBlocks[]> {
  try {
    // Временно упрощаем запрос, так как текущая структура коллекции не имеет полей section, is_active, sort_order
    const records = await pb.collection('html_blocks').getFullList({
      sort: 'created',
      requestKey: null,
    });

    console.log(`Получено HTML блоков из PocketBase${section ? ` для секции ${section}` : ''}:`, records.length);

    // Преобразуем данные в ожидаемый формат
    const transformedRecords = records.map(record => ({
      id: record.id,
      key: record.key || record.title?.toLowerCase().replace(/\s+/g, '_') || record.id,
      title: record.title || '',
      content: record.content || '',
      section: section || 'default',
      type: 'text',
      is_active: true,
      sort_order: 0,
      created: record.created,
      updated: record.updated
    }));

    return transformedRecords as unknown as HtmlBlocks[];
  } catch (error) {
    console.error('Ошибка при получении HTML блоков из PocketBase:', error);
    return [];
  }
}

/**
 * Получить HTML блок по ключу
 */
export async function getHtmlBlockByKey(key: string): Promise<HtmlBlocks | null> {
  try {
    // Ищем по полю key
    const record = await pb.collection('html_blocks').getFirstListItem(`key="${key}"`);

    return {
      id: record.id,
      key: record.key || record.title?.toLowerCase().replace(/\s+/g, '_') || record.id,
      title: record.title || '',
      content: record.content || '',
      section: 'default',
      type: 'text',
      is_active: true,
      sort_order: 0,
      created: record.created,
      updated: record.updated
    } as any;
  } catch (error) {
    console.error(`Ошибка при получении HTML блока с ключом ${key}:`, error);
    return null;
  }
}

/**
 * Получить HTML блоки для конкретной секции, организованные по типам
 */
export async function getHtmlBlocksForSection(section: string): Promise<Record<string, any[]>> {
  try {
    const blocks = await getHtmlBlocks(section);

    // Группируем блоки по типам
    const groupedBlocks: Record<string, any[]> = {};

    blocks.forEach(block => {
      if (!groupedBlocks[block.type]) {
        groupedBlocks[block.type] = [];
      }
      groupedBlocks[block.type].push(block);
    });

    return groupedBlocks;
  } catch (error) {
    console.error(`Ошибка при получении HTML блоков для секции ${section}:`, error);
    return {};
  }
}


