'use client'

import { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { Calendar, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { type Promo } from '@/lib/api'

interface PromosSectionProps {
  promos: Promo[]
}

export default function PromosSection({ promos }: PromosSectionProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: true, margin: '-100px 0px' })

  // Фильтруем только активные и избранные акции
  const featuredPromos = promos.filter(promo => promo.is_active && promo.is_featured)

  // Если нет ни одной акции — скрываем весь раздел
  if (featuredPromos.length === 0) return null

  return (
    <section ref={containerRef} className="bg-white py-20">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <Badge className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 mb-4 text-white shadow-lg">Специальные предложения</Badge>
          <h2 className="mb-4 text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 via-olive-800 to-gray-900 bg-clip-text text-transparent">Акции и скидки</h2>
          <p className="text-lg text-gray-600">
            Воспользуйтесь нашими специальными предложениями и получите качественное лечение по выгодной цене
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {featuredPromos.map((promo, index) => (
            <motion.div
              key={promo.id}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="group overflow-hidden rounded-xl bg-white shadow-md transition-all hover:shadow-lg"
            >
              <div className="relative h-48 overflow-hidden">
                {promo.image ? (
                  <img
                    src={`${import.meta.env.PUBLIC_API_URL || 'https://pb.stom-line.ru'}/api/files/promos/${promo.id}/${promo.image}`}
                    alt={promo.title}
                    className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-olive-100 text-olive-600">
                    <span className="text-xl font-bold">АКЦИЯ</span>
                  </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

                {(promo.start_date || promo.end_date) && (
                  <div className="absolute bottom-4 left-4 flex items-center rounded-full bg-white/90 px-3 py-1 text-sm backdrop-blur-sm">
                    <Calendar className="mr-1 h-4 w-4 text-olive-600" />
                    <span>
                      {promo.start_date && new Date(promo.start_date).toLocaleDateString('ru-RU')}
                      {promo.start_date && promo.end_date && ' - '}
                      {promo.end_date && new Date(promo.end_date).toLocaleDateString('ru-RU')}
                    </span>
                  </div>
                )}
              </div>

              <div className="p-6">
                <h3 className="mb-2 text-xl font-bold">{promo.title}</h3>
                {promo.subtitle && <p className="mb-4 text-gray-600">{promo.subtitle}</p>}

                <div className="prose prose-olive line-clamp-3 max-w-none" dangerouslySetInnerHTML={{ __html: promo.content || '' }} />

                <div className="mt-4">
                  <Button variant="link" className="text-olive-600 h-auto p-0 font-medium">
                    Подробнее <ArrowRight className="ml-1 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Button className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-xl">
            Все акции и специальные предложения
          </Button>
        </div>
      </div>
    </section>
  )
}
