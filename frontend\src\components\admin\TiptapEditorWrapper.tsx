import React, { useState, useCallback, useRef } from 'react'
import { Editor<PERSON>ontent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import Link from '@tiptap/extension-link'
import Underline from '@tiptap/extension-underline'
import Image from '@tiptap/extension-image'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import TaskList from '@tiptap/extension-task-list'
import TaskItem from '@tiptap/extension-task-item'
import Blockquote from '@tiptap/extension-blockquote'
import CodeBlock from '@tiptap/extension-code-block'
import Color from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import Highlight from '@tiptap/extension-highlight'
import HorizontalRule from '@tiptap/extension-horizontal-rule'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'
import Typography from '@tiptap/extension-typography'
import Placeholder from '@tiptap/extension-placeholder'
import { Button } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog'
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,
  Quote,
  Code,
  Image as ImageIcon,
  Link as LinkIcon,
  Unlink,
  Table as TableIcon,
  Plus,
  Minus,
  Trash2,
  Palette,
  Highlighter,
  Subscript as SubscriptIcon,
  Superscript as SuperscriptIcon,
  CheckSquare,
  Undo,
  Redo,
  Eye,
  Code2,
  Upload,
  Save,
  RotateCcw,
  Heading1,
  Heading2,
  Heading3,
  PilcrowIcon,
  SeparatorHorizontal
} from 'lucide-react'
import { cn } from '@/lib/utils'
import pb, { getFileUrl } from '@/lib/pocketbase'
import './TiptapEditor.css'

interface TiptapEditorWrapperProps {
  id?: string
  value: string
  onChange: (value: string) => void
  readOnly?: boolean
  style?: React.CSSProperties
  placeholder?: string
  className?: string
  autoSave?: boolean
  onAutoSave?: (content: string) => Promise<void> | void
  autoSaveInterval?: number
}

interface ToolbarButtonProps {
  onClick: () => void
  isActive?: boolean
  disabled?: boolean
  children: React.ReactNode
  tooltip?: string
  variant?: 'default' | 'outline' | 'ghost'
}

interface ColorPickerProps {
  onColorSelect: (color: string) => void
  currentColor?: string
}

interface LinkDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (url: string, text?: string) => void
  currentUrl?: string
  currentText?: string
}

interface ImageDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (src: string, alt?: string) => void
}

interface TableDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (rows: number, cols: number, withHeader: boolean) => void
}

// Вспомогательные компоненты
const ToolbarButton: React.FC<ToolbarButtonProps> = ({
  onClick,
  isActive = false,
  disabled = false,
  children,
  tooltip,
  variant = 'outline'
}) => (
  <Button
    onClick={onClick}
    disabled={disabled}
    variant={variant}
    size="sm"
    className={cn(
      "h-8 w-8 p-0 transition-all duration-200",
      isActive && "bg-[#8BC34A] text-white hover:bg-[#4E8C29]",
      !isActive && "hover:bg-[#85C026]/10"
    )}
    title={tooltip}
  >
    {children}
  </Button>
)

const ColorPicker: React.FC<ColorPickerProps> = ({ onColorSelect, currentColor }) => {
  const colors = [
    '#000000', '#8BC34A', '#4E8C29', '#85C026', '#FF5722', '#2196F3',
    '#9C27B0', '#FF9800', '#607D8B', '#795548', '#E91E63', '#00BCD4'
  ]

  return (
    <div className="grid grid-cols-6 gap-1 p-2">
      {colors.map((color) => (
        <button
          key={color}
          onClick={() => onColorSelect(color)}
          className={cn(
            "w-6 h-6 rounded border-2 transition-all",
            currentColor === color ? "border-gray-800 scale-110" : "border-gray-300 hover:scale-105"
          )}
          style={{ backgroundColor: color }}
          title={color}
        />
      ))}
    </div>
  )
}

const LinkDialog: React.FC<LinkDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  currentUrl = '',
  currentText = ''
}) => {
  const [url, setUrl] = useState(currentUrl)
  const [text, setText] = useState(currentText)

  const handleSubmit = () => {
    if (url.trim()) {
      onSubmit(url.trim(), text.trim() || undefined)
      onClose()
      setUrl('')
      setText('')
    }
  }

  React.useEffect(() => {
    if (isOpen) {
      setUrl(currentUrl)
      setText(currentText)
    }
  }, [isOpen, currentUrl, currentText])

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Добавить ссылку</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="url">URL</Label>
            <Input
              id="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="mt-1"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSubmit()
                }
              }}
            />
          </div>
          <div>
            <Label htmlFor="text">Текст ссылки (необязательно)</Label>
            <Input
              id="text"
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Текст ссылки"
              className="mt-1"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSubmit()
                }
              }}
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Отмена
            </Button>
            <Button onClick={handleSubmit} disabled={!url.trim()}>
              Добавить
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

const ImageDialog: React.FC<ImageDialogProps> = ({ isOpen, onClose, onSubmit }) => {
  const [src, setSrc] = useState('')
  const [alt, setAlt] = useState('')

  const handleSubmit = () => {
    if (src.trim()) {
      onSubmit(src.trim(), alt.trim() || undefined)
      onClose()
      setSrc('')
      setAlt('')
    }
  }

  React.useEffect(() => {
    if (!isOpen) {
      setSrc('')
      setAlt('')
    }
  }, [isOpen])

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Добавить изображение</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="image-src">URL изображения</Label>
            <Input
              id="image-src"
              value={src}
              onChange={(e) => setSrc(e.target.value)}
              placeholder="https://example.com/image.jpg"
              className="mt-1"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  handleSubmit()
                }
              }}
            />
          </div>
          <div>
            <Label htmlFor="image-alt">Альтернативный текст (необязательно)</Label>
            <Input
              id="image-alt"
              value={alt}
              onChange={(e) => setAlt(e.target.value)}
              placeholder="Описание изображения"
              className="mt-1"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  handleSubmit()
                }
              }}
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Отмена
            </Button>
            <Button onClick={handleSubmit} disabled={!src.trim()}>
              Добавить
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

const TableDialog: React.FC<TableDialogProps> = ({ isOpen, onClose, onSubmit }) => {
  const [rows, setRows] = useState(3)
  const [cols, setCols] = useState(3)
  const [withHeader, setWithHeader] = useState(true)

  const handleSubmit = () => {
    onSubmit(rows, cols, withHeader)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Создать таблицу</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="table-rows">Строки</Label>
              <Input
                id="table-rows"
                type="number"
                min="1"
                max="20"
                value={rows}
                onChange={(e) => setRows(parseInt(e.target.value) || 1)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="table-cols">Столбцы</Label>
              <Input
                id="table-cols"
                type="number"
                min="1"
                max="10"
                value={cols}
                onChange={(e) => setCols(parseInt(e.target.value) || 1)}
                className="mt-1"
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="with-header"
              checked={withHeader}
              onChange={(e) => setWithHeader(e.target.checked)}
              className="rounded"
            />
            <Label htmlFor="with-header">Добавить заголовок</Label>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Отмена
            </Button>
            <Button onClick={handleSubmit}>
              Создать
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export const TiptapEditorWrapper: React.FC<TiptapEditorWrapperProps> = ({
  id,
  value,
  onChange,
  readOnly = false,
  style = {},
  placeholder = "Начните вводить текст...",
  className,
  autoSave = false,
  onAutoSave,
  autoSaveInterval = 5000
}) => {
  // Состояние компонента
  const [isSourceMode, setIsSourceMode] = useState(false)
  const [sourceContent, setSourceContent] = useState('')
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false)
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false)
  const [isTableDialogOpen, setIsTableDialogOpen] = useState(false)
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [showHighlightPicker, setShowHighlightPicker] = useState(false)
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Конфигурация редактора
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph']
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-[#8BC34A] hover:text-[#4E8C29] underline',
        },
      }),
      Underline,
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'border-collapse border border-gray-300 w-full',
        },
      }),
      TableRow.configure({
        HTMLAttributes: {
          class: 'border border-gray-300',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-gray-300 p-2',
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: 'border border-gray-300 p-2 bg-gray-50 font-semibold',
        },
      }),
      TaskList.configure({
        HTMLAttributes: {
          class: 'list-none',
        },
      }),
      TaskItem.configure({
        nested: true,
        HTMLAttributes: {
          class: 'flex items-start',
        },
      }),
      Blockquote.configure({
        HTMLAttributes: {
          class: 'border-l-4 border-[#8BC34A] pl-4 italic text-gray-600',
        },
      }),
      CodeBlock.configure({
        HTMLAttributes: {
          class: 'bg-gray-100 rounded p-4 font-mono text-sm',
        },
      }),
      Color,
      TextStyle,
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 px-1 rounded',
        },
      }),
      HorizontalRule.configure({
        HTMLAttributes: {
          class: 'border-t-2 border-gray-300 my-4',
        },
      }),
      Subscript,
      Superscript,
      Typography,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: value,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      onChange(html)

      // Автосохранение
      if (autoSave && onAutoSave) {
        if (autoSaveTimeoutRef.current) {
          clearTimeout(autoSaveTimeoutRef.current)
        }
        setAutoSaveStatus('saving')
        autoSaveTimeoutRef.current = setTimeout(async () => {
          try {
            await onAutoSave(html)
            setAutoSaveStatus('saved')
            setTimeout(() => setAutoSaveStatus('idle'), 2000)
          } catch (error) {
            console.error('Ошибка автосохранения:', error)
            setAutoSaveStatus('error')
            setTimeout(() => setAutoSaveStatus('idle'), 3000)
          }
        }, autoSaveInterval)
      }
    },
    onCreate: ({ editor }) => {
      if (isSourceMode) {
        setSourceContent(editor.getHTML())
      }
    },
  })

  // Обработчики событий
  const handleLinkSubmit = useCallback((url: string, text?: string) => {
    if (!editor) return

    if (text) {
      editor.chain().focus().insertContent(`<a href="${url}">${text}</a>`).run()
    } else {
      editor.chain().focus().setLink({ href: url }).run()
    }
  }, [editor])

  const handleImageSubmit = useCallback((src: string, alt?: string) => {
    if (!editor) return
    editor.chain().focus().setImage({ src, alt }).run()
  }, [editor])

  const handleTableSubmit = useCallback((rows: number, cols: number, withHeader: boolean) => {
    if (!editor) return
    editor.chain().focus().insertTable({ rows, cols, withHeaderRow: withHeader }).run()
  }, [editor])

  const handleFileUpload = useCallback(async (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('Пожалуйста, выберите изображение')
      return
    }

    try {
      // Проверяем размер файла (максимум 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Размер файла не должен превышать 5MB')
        return
      }

      // Создаем FormData для загрузки файла
      const formData = new FormData()
      formData.append('file', file)

      // Попытка загрузки в PocketBase (если доступен)
      try {
        // Создаем временную запись для хранения файла
        const record = await pb.collection('files').create(formData)
        const fileUrl = getFileUrl('files', record.id, record.file)
        handleImageSubmit(fileUrl, file.name)
      } catch (pbError) {
        console.warn('PocketBase недоступен, используем локальный URL:', pbError)
        // Fallback: используем локальный URL
        const url = URL.createObjectURL(file)
        handleImageSubmit(url, file.name)
      }
    } catch (error) {
      console.error('Ошибка загрузки файла:', error)
      alert('Ошибка загрузки файла')
    }
  }, [handleImageSubmit])

  const toggleSourceMode = useCallback(() => {
    if (!editor) return

    if (isSourceMode) {
      // Переключаемся обратно в визуальный режим
      editor.commands.setContent(sourceContent)
      setIsSourceMode(false)
    } else {
      // Переключаемся в режим исходного кода
      setSourceContent(editor.getHTML())
      setIsSourceMode(true)
    }
  }, [editor, isSourceMode, sourceContent])

  const handleSourceContentChange = useCallback((newContent: string) => {
    setSourceContent(newContent)
    onChange(newContent)
  }, [onChange])

  // Эффекты
  React.useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value || '', false)
    }
  }, [value, editor])

  React.useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
    }
  }, [])

  if (!editor) {
    return (
      <div className={cn("min-h-[200px] rounded-lg border bg-card", className)} style={style}>
        <div className="flex items-center justify-center h-full">
          <div className="text-muted-foreground">Загрузка редактора...</div>
        </div>
      </div>
    )
  }

  return (
    <Card id={id} style={style} className={cn("min-h-[600px]", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">HTML Редактор</CardTitle>
          <div className="flex items-center space-x-2">
            {autoSave && (
              <div className="flex items-center space-x-2 text-sm">
                {autoSaveStatus === 'saving' && (
                  <div className="flex items-center text-blue-600">
                    <div className="animate-spin h-3 w-3 border border-blue-600 border-t-transparent rounded-full mr-1"></div>
                    Сохранение...
                  </div>
                )}
                {autoSaveStatus === 'saved' && (
                  <div className="flex items-center text-green-600">
                    <Save className="h-3 w-3 mr-1" />
                    Сохранено
                  </div>
                )}
                {autoSaveStatus === 'error' && (
                  <div className="flex items-center text-red-600">
                    <span className="h-3 w-3 mr-1">⚠</span>
                    Ошибка сохранения
                  </div>
                )}
                {autoSaveStatus === 'idle' && (
                  <div className="text-muted-foreground">
                    Автосохранение включено
                  </div>
                )}
              </div>
            )}
            <ToolbarButton
              onClick={toggleSourceMode}
              isActive={isSourceMode}
              tooltip={isSourceMode ? "Визуальный режим" : "Режим кода"}
            >
              {isSourceMode ? <Eye className="h-4 w-4" /> : <Code2 className="h-4 w-4" />}
            </ToolbarButton>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {!isSourceMode && (
          <Tabs defaultValue="format" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="format">Форматирование</TabsTrigger>
              <TabsTrigger value="structure">Структура</TabsTrigger>
              <TabsTrigger value="media">Медиа</TabsTrigger>
              <TabsTrigger value="advanced">Дополнительно</TabsTrigger>
            </TabsList>

            {/* Панель форматирования */}
            <TabsContent value="format" className="space-y-3">
              <div className="flex flex-wrap gap-1">
                {/* Отмена/Повтор */}
                <div className="flex border-r pr-2 mr-2">
                  <ToolbarButton
                    onClick={() => editor.chain().focus().undo().run()}
                    disabled={!editor.can().undo()}
                    tooltip="Отменить"
                  >
                    <Undo className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().redo().run()}
                    disabled={!editor.can().redo()}
                    tooltip="Повторить"
                  >
                    <Redo className="h-4 w-4" />
                  </ToolbarButton>
                </div>

                {/* Базовое форматирование */}
                <div className="flex border-r pr-2 mr-2">
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleBold().run()}
                    isActive={editor.isActive('bold')}
                    tooltip="Жирный"
                  >
                    <Bold className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleItalic().run()}
                    isActive={editor.isActive('italic')}
                    tooltip="Курсив"
                  >
                    <Italic className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleUnderline().run()}
                    isActive={editor.isActive('underline')}
                    tooltip="Подчеркнутый"
                  >
                    <UnderlineIcon className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleStrike().run()}
                    isActive={editor.isActive('strike')}
                    tooltip="Зачеркнутый"
                  >
                    <Strikethrough className="h-4 w-4" />
                  </ToolbarButton>
                </div>

                {/* Выравнивание */}
                <div className="flex border-r pr-2 mr-2">
                  <ToolbarButton
                    onClick={() => editor.chain().focus().setTextAlign('left').run()}
                    isActive={editor.isActive({ textAlign: 'left' })}
                    tooltip="По левому краю"
                  >
                    <AlignLeft className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().setTextAlign('center').run()}
                    isActive={editor.isActive({ textAlign: 'center' })}
                    tooltip="По центру"
                  >
                    <AlignCenter className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().setTextAlign('right').run()}
                    isActive={editor.isActive({ textAlign: 'right' })}
                    tooltip="По правому краю"
                  >
                    <AlignRight className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().setTextAlign('justify').run()}
                    isActive={editor.isActive({ textAlign: 'justify' })}
                    tooltip="По ширине"
                  >
                    <AlignJustify className="h-4 w-4" />
                  </ToolbarButton>
                </div>

                {/* Цвета */}
                <div className="flex">
                  <ToolbarButton
                    onClick={() => setShowColorPicker(!showColorPicker)}
                    tooltip="Цвет текста"
                  >
                    <Palette className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => setShowHighlightPicker(!showHighlightPicker)}
                    isActive={editor.isActive('highlight')}
                    tooltip="Выделение"
                  >
                    <Highlighter className="h-4 w-4" />
                  </ToolbarButton>
                </div>
              </div>

              {/* Цветовые палитры */}
              {showColorPicker && (
                <div className="border rounded-lg p-2 bg-popover">
                  <div className="text-sm font-medium mb-2">Цвет текста</div>
                  <ColorPicker
                    onColorSelect={(color) => {
                      editor.chain().focus().setColor(color).run()
                      setShowColorPicker(false)
                    }}
                  />
                </div>
              )}

              {showHighlightPicker && (
                <div className="border rounded-lg p-2 bg-popover">
                  <div className="text-sm font-medium mb-2">Цвет выделения</div>
                  <ColorPicker
                    onColorSelect={(color) => {
                      editor.chain().focus().toggleHighlight({ color }).run()
                      setShowHighlightPicker(false)
                    }}
                  />
                </div>
              )}
            </TabsContent>

            {/* Панель структуры */}
            <TabsContent value="structure" className="space-y-3">
              <div className="flex flex-wrap gap-1">
                {/* Заголовки */}
                <div className="flex border-r pr-2 mr-2">
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                    isActive={editor.isActive('heading', { level: 1 })}
                    tooltip="Заголовок 1"
                  >
                    <Heading1 className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                    isActive={editor.isActive('heading', { level: 2 })}
                    tooltip="Заголовок 2"
                  >
                    <Heading2 className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                    isActive={editor.isActive('heading', { level: 3 })}
                    tooltip="Заголовок 3"
                  >
                    <Heading3 className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().setParagraph().run()}
                    isActive={editor.isActive('paragraph')}
                    tooltip="Обычный текст"
                  >
                    <PilcrowIcon className="h-4 w-4" />
                  </ToolbarButton>
                </div>

                {/* Списки */}
                <div className="flex border-r pr-2 mr-2">
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleBulletList().run()}
                    isActive={editor.isActive('bulletList')}
                    tooltip="Маркированный список"
                  >
                    <List className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleOrderedList().run()}
                    isActive={editor.isActive('orderedList')}
                    tooltip="Нумерованный список"
                  >
                    <ListOrdered className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleTaskList().run()}
                    isActive={editor.isActive('taskList')}
                    tooltip="Список задач"
                  >
                    <CheckSquare className="h-4 w-4" />
                  </ToolbarButton>
                </div>

                {/* Блоки */}
                <div className="flex">
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleBlockquote().run()}
                    isActive={editor.isActive('blockquote')}
                    tooltip="Цитата"
                  >
                    <Quote className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().toggleCodeBlock().run()}
                    isActive={editor.isActive('codeBlock')}
                    tooltip="Блок кода"
                  >
                    <Code className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().setHorizontalRule().run()}
                    tooltip="Горизонтальная линия"
                  >
                    <SeparatorHorizontal className="h-4 w-4" />
                  </ToolbarButton>
                </div>
              </div>
            </TabsContent>

            {/* Панель медиа */}
            <TabsContent value="media" className="space-y-3">
              <div className="flex flex-wrap gap-1">
                <ToolbarButton
                  onClick={() => setIsLinkDialogOpen(true)}
                  isActive={editor.isActive('link')}
                  tooltip="Добавить ссылку"
                >
                  <LinkIcon className="h-4 w-4" />
                </ToolbarButton>
                <ToolbarButton
                  onClick={() => editor.chain().focus().unsetLink().run()}
                  disabled={!editor.isActive('link')}
                  tooltip="Удалить ссылку"
                >
                  <Unlink className="h-4 w-4" />
                </ToolbarButton>
                <ToolbarButton
                  onClick={() => setIsImageDialogOpen(true)}
                  tooltip="Добавить изображение"
                >
                  <ImageIcon className="h-4 w-4" />
                </ToolbarButton>
                <ToolbarButton
                  onClick={() => fileInputRef.current?.click()}
                  tooltip="Загрузить файл"
                >
                  <Upload className="h-4 w-4" />
                </ToolbarButton>
                <ToolbarButton
                  onClick={() => setIsTableDialogOpen(true)}
                  tooltip="Добавить таблицу"
                >
                  <TableIcon className="h-4 w-4" />
                </ToolbarButton>
              </div>

              {/* Управление таблицами */}
              {editor.isActive('table') && (
                <div className="flex flex-wrap gap-1 p-2 bg-muted rounded">
                  <div className="text-sm font-medium mr-2">Таблица:</div>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().addColumnBefore().run()}
                    tooltip="Добавить столбец слева"
                  >
                    <Plus className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().addColumnAfter().run()}
                    tooltip="Добавить столбец справа"
                  >
                    <Plus className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().deleteColumn().run()}
                    tooltip="Удалить столбец"
                  >
                    <Minus className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().addRowBefore().run()}
                    tooltip="Добавить строку сверху"
                  >
                    <Plus className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().addRowAfter().run()}
                    tooltip="Добавить строку снизу"
                  >
                    <Plus className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().deleteRow().run()}
                    tooltip="Удалить строку"
                  >
                    <Minus className="h-4 w-4" />
                  </ToolbarButton>
                  <ToolbarButton
                    onClick={() => editor.chain().focus().deleteTable().run()}
                    tooltip="Удалить таблицу"
                  >
                    <Trash2 className="h-4 w-4" />
                  </ToolbarButton>
                </div>
              )}
            </TabsContent>

            {/* Панель дополнительных функций */}
            <TabsContent value="advanced" className="space-y-3">
              <div className="flex flex-wrap gap-1">
                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleSuperscript().run()}
                  isActive={editor.isActive('superscript')}
                  tooltip="Верхний индекс"
                >
                  <SuperscriptIcon className="h-4 w-4" />
                </ToolbarButton>
                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleSubscript().run()}
                  isActive={editor.isActive('subscript')}
                  tooltip="Нижний индекс"
                >
                  <SubscriptIcon className="h-4 w-4" />
                </ToolbarButton>
                <ToolbarButton
                  onClick={() => editor.chain().focus().unsetAllMarks().clearNodes().run()}
                  tooltip="Очистить форматирование"
                >
                  <RotateCcw className="h-4 w-4" />
                </ToolbarButton>
              </div>
            </TabsContent>
          </Tabs>
        )}

        {/* Режим исходного кода */}
        {isSourceMode ? (
          <div className="space-y-2">
            <Label htmlFor="source-editor">HTML код</Label>
            <Textarea
              id="source-editor"
              value={sourceContent}
              onChange={(e) => handleSourceContentChange(e.target.value)}
              className="min-h-[400px] font-mono text-sm text-gray-900 bg-white"
              placeholder="Введите HTML код..."
            />
          </div>
        ) : (
          <div className="min-h-[400px] border rounded-lg p-4 bg-white tiptap-light-theme">
            <EditorContent
              editor={editor}
              className="prose prose-sm max-w-none focus:outline-none [&_.ProseMirror]:outline-none [&_.ProseMirror]:min-h-[350px] [&_.ProseMirror]:p-4 [&_.ProseMirror]:text-gray-900 [&_.ProseMirror]:bg-white"
            />
          </div>
        )}

        {/* Скрытый input для загрузки файлов */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={(e) => {
            const file = e.target.files?.[0]
            if (file) {
              handleFileUpload(file)
            }
          }}
        />

        {/* Диалоги */}
        <LinkDialog
          isOpen={isLinkDialogOpen}
          onClose={() => setIsLinkDialogOpen(false)}
          onSubmit={handleLinkSubmit}
        />

        <ImageDialog
          isOpen={isImageDialogOpen}
          onClose={() => setIsImageDialogOpen(false)}
          onSubmit={handleImageSubmit}
        />

        <TableDialog
          isOpen={isTableDialogOpen}
          onClose={() => setIsTableDialogOpen(false)}
          onSubmit={handleTableSubmit}
        />
      </CardContent>
    </Card>
  )
}
