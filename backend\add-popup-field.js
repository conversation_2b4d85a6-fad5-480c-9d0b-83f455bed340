#!/usr/bin/env node

/**
 * Скрипт для добавления поля is_popup_enabled в коллекцию promos
 * Запускается из директории backend
 */

const PocketBase = require('pocketbase/cjs');

async function addPopupField() {
  const pb = new PocketBase('http://localhost:8090');
  
  try {
    // Авторизация как администратор
    await pb.admins.authWithPassword(
      process.env.ADMIN_EMAIL || '<EMAIL>',
      process.env.ADMIN_PASSWORD || 'admin123456'
    );
    
    console.log('✅ Успешная авторизация в PocketBase');
    
    // Получаем коллекцию promos
    const collection = await pb.collections.getOne('promos');
    console.log('✅ Найдена коллекция promos');
    
    // Проверяем, есть ли уже поле is_popup_enabled
    const existingField = collection.schema.find(field => field.name === 'is_popup_enabled');
    if (existingField) {
      console.log('⚠️  Поле is_popup_enabled уже существует');
      return;
    }
    
    // Добавляем новое поле
    const newField = {
      name: 'is_popup_enabled',
      type: 'bool',
      required: false,
      presentable: false,
      options: {}
    };
    
    collection.schema.push(newField);
    
    // Обновляем коллекцию
    await pb.collections.update(collection.id, {
      schema: collection.schema
    });
    
    console.log('✅ Поле is_popup_enabled успешно добавлено в коллекцию promos');
    
  } catch (error) {
    console.error('❌ Ошибка при добавлении поля:', error);
    process.exit(1);
  }
}

// Запускаем скрипт
addPopupField();
