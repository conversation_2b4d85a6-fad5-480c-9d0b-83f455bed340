import * as React from 'react';
import { Button } from './button';

interface MultiSelectOption {
  value: string;
  label: string;
}

interface MultiSelectProps {
  id?: string;
  name: string;
  options: MultiSelectOption[];
  value: string[];
  onChange: (value: string[]) => void;
  disabled?: boolean;
  placeholder?: string;
}

export const MultiSelect: React.FC<MultiSelectProps> = ({
  id,
  name,
  options,
  value,
  onChange,
  disabled = false,
  placeholder = 'Выберите...'
}) => {
  const [open, setOpen] = React.useState(false);
  const toggle = () => setOpen((v) => !v);
  const handleSelect = (val: string) => {
    if (value.includes(val)) {
      onChange(value.filter((v) => v !== val));
    } else {
      onChange([...value, val]);
    }
  };
  return (
    <div className="relative">
      <Button type="button" onClick={toggle} disabled={disabled} className="w-full justify-between">
        {value.length === 0 ? (
          <span className="text-gray-400">{placeholder}</span>
        ) : (
          <span>{options.filter(o => value.includes(o.value)).map(o => o.label).join(', ')}</span>
        )}
        <span className="ml-2">▼</span>
      </Button>
      {open && (
        <div className="absolute z-10 mt-1 w-full rounded border bg-white shadow-lg max-h-60 overflow-auto">
          {options.map(opt => (
            <label key={opt.value} className="flex items-center px-3 py-2 hover:bg-gray-100 cursor-pointer">
              <input
                type="checkbox"
                checked={value.includes(opt.value)}
                onChange={() => handleSelect(opt.value)}
                className="mr-2"
                disabled={disabled}
              />
              {opt.label}
            </label>
          ))}
        </div>
      )}
    </div>
  );
};
