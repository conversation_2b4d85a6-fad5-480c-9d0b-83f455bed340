#!/usr/bin/env node

/**
 * Скрипт для импорта HTML блоков в PocketBase
 * Использование: bun import-html-blocks.js
 */

import PocketBase from 'pocketbase';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Конфигурация PocketBase
const PB_URL = process.env.POCKETBASE_URL || 'https://pb.stom-line.ru';
const PB_EMAIL = process.env.POCKETBASE_ADMIN_EMAIL || '<EMAIL>';
const PB_PASSWORD = process.env.POCKETBASE_ADMIN_PASSWORD || '!10Havafi1';

async function importHtmlBlocks() {
  try {
    console.log('🚀 Начинаем импорт HTML блоков...');
    
    // Инициализация PocketBase
    const pb = new PocketBase(PB_URL);
    
    // Авторизация администратора (PocketBase v0.23.0+)
    if (!PB_EMAIL || !PB_PASSWORD) {
      console.error('❌ Не указаны переменные окружения POCKETBASE_ADMIN_EMAIL и POCKETBASE_ADMIN_PASSWORD');
      process.exit(1);
    }

    console.log('🔐 Авторизация в PocketBase...');
    // Авторизация через коллекцию _superusers (PocketBase v0.23.0+)
    await pb.collection('_superusers').authWithPassword(PB_EMAIL, PB_PASSWORD);
    console.log('✅ Авторизация успешна');
    
    // Загрузка данных из JSON файлов
    const dataPath = path.join(__dirname, '../data/html-blocks-import.json');
    const achievementsDataPath = path.join(__dirname, '../data/achievements-blocks-import.json');

    const htmlBlocksData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
    const achievementsBlocksData = JSON.parse(fs.readFileSync(achievementsDataPath, 'utf8'));

    // Объединяем данные
    const allBlocksData = [...htmlBlocksData, ...achievementsBlocksData];
    
    console.log(`📦 Загружено ${allBlocksData.length} HTML блоков для импорта (${htmlBlocksData.length} основных + ${achievementsBlocksData.length} для достижений)`);

    // Проверяем существующие записи
    console.log('🔍 Проверяем существующие записи...');
    const existingBlocks = await pb.collection('html_blocks').getFullList();
    const existingKeys = new Set(existingBlocks.map(block => block.key));

    let created = 0;
    let updated = 0;
    let skipped = 0;

    // Импорт каждого блока
    for (const blockData of allBlocksData) {
      try {
        if (existingKeys.has(blockData.key)) {
          // Обновляем существующую запись
          const existingBlock = existingBlocks.find(block => block.key === blockData.key);
          await pb.collection('html_blocks').update(existingBlock.id, blockData);
          console.log(`🔄 Обновлен блок: ${blockData.key}`);
          updated++;
        } else {
          // Создаем новую запись
          await pb.collection('html_blocks').create(blockData);
          console.log(`✨ Создан блок: ${blockData.key}`);
          created++;
        }
      } catch (error) {
        console.error(`❌ Ошибка при обработке блока ${blockData.key}:`, error.message);
        skipped++;
      }
    }
    
    console.log('\n📊 Результаты импорта:');
    console.log(`✅ Создано: ${created}`);
    console.log(`🔄 Обновлено: ${updated}`);
    console.log(`⚠️  Пропущено: ${skipped}`);
    console.log(`📦 Всего обработано: ${created + updated + skipped}`);
    
    console.log('\n🎉 Импорт HTML блоков завершен!');
    
  } catch (error) {
    console.error('❌ Ошибка при импорте HTML блоков:', error);
    process.exit(1);
  }
}

// Запуск скрипта
importHtmlBlocks();
