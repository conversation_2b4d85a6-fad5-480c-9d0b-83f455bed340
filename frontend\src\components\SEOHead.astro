---
import { seoConfig } from '@/lib/seo-config';
import SocialMetaTags from './SocialMetaTags.astro';

export interface Props {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  type?: string;
  url?: string;
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
  jsonLd?: any;
  article?: {
    publishedTime?: string;
    modifiedTime?: string;
    author?: string;
    section?: string;
    tags?: string[];
  };
}

const {
  title = seoConfig.defaults.title,
  description = seoConfig.defaults.description,
  keywords = seoConfig.site.keywords,
  image = seoConfig.defaults.image,
  type = seoConfig.defaults.type,
  url = seoConfig.site.url,
  canonical,
  noindex = false,
  nofollow = false,
  jsonLd,
  article
} = Astro.props;

// Формируем полный URL для изображения
const fullImageUrl = image.startsWith('http') ? image : `${seoConfig.site.url}${image}`;

// Формируем полный URL страницы
const fullUrl = url.startsWith('http') ? url : `${seoConfig.site.url}${url}`;

// Формируем canonical URL
const canonicalUrl = canonical || fullUrl;

// Формируем robots meta
const robotsContent = [
  noindex ? 'noindex' : 'index',
  nofollow ? 'nofollow' : 'follow'
].join(', ');

// Формируем keywords строку
const keywordsString = Array.isArray(keywords) ? keywords.join(', ') : keywords;
---

<!-- Основные мета-теги -->
<title>{title}</title>
<meta name="description" content={description} />
<meta name="keywords" content={keywordsString} />
<meta name="author" content={seoConfig.site.author} />
<meta name="robots" content={robotsContent} />
<link rel="canonical" href={canonicalUrl} />

<!-- Социальные сети и Open Graph -->
<SocialMetaTags
  title={title}
  description={description}
  image={fullImageUrl}
  url={fullUrl}
  type={type}
  siteName={seoConfig.site.name}
  locale={seoConfig.site.locale}
  article={article}
/>

<!-- Дополнительные мета-теги для поисковых систем -->
<meta name="theme-color" content="#8BC34A" />
<meta name="msapplication-TileColor" content="#8BC34A" />
<meta name="application-name" content={seoConfig.site.name} />

<!-- Языковые мета-теги -->
<meta http-equiv="content-language" content={seoConfig.site.language} />
<meta name="language" content={seoConfig.site.language} />

<!-- Мета-теги для мобильных устройств -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
<meta name="format-detection" content="telephone=yes" />
<meta name="format-detection" content="address=yes" />

<!-- Мета-теги для доступности -->
<meta name="accessibility-features" content="high-contrast, large-text, keyboard-navigation, screen-reader-support" />
<meta name="accessibility-compliance" content="GOST R 52872-2019, WCAG 2.1 AA" />
<meta name="accessibility-help" content="Для включения версии для слабовидящих нажмите кнопку 'Для слабовидящих' в верхней части страницы" />

<!-- RDFa разметка для Яндекса -->
<meta property="ya:ovs:adult" content="false" />
<meta property="ya:ovs:allow_adult" content="false" />

<!-- Структурированные данные JSON-LD -->
{jsonLd && (
  <script type="application/ld+json" set:html={JSON.stringify(jsonLd)} is:inline />
)}

<!-- Дополнительные ссылки -->
<link rel="sitemap" href="/sitemap.xml" />
<link rel="alternate" type="application/rss+xml" title={`${seoConfig.site.name} RSS`} href="/rss.xml" />

<!-- Preconnect для внешних ресурсов -->
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link rel="preconnect" href="https://pb.stom-line.ru" />

<!-- DNS prefetch для улучшения производительности -->
<link rel="dns-prefetch" href="//fonts.googleapis.com" />
<link rel="dns-prefetch" href="//fonts.gstatic.com" />
<link rel="dns-prefetch" href="//pb.stom-line.ru" />
<link rel="dns-prefetch" href="//search.stom-line.ru" />

<!-- Favicon и иконки -->
<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
<link rel="manifest" href="/site.webmanifest" />
