/**
 * Скрипт для тестирования уведомлений
 * 
 * Создает тестовую заявку в коллекции callback_requests
 * для проверки работы уведомлений
 * 
 * Использование:
 * 1. Убедитесь, что PocketBase запущен
 * 2. Настройте переменные окружения для уведомлений
 * 3. Запустите: node test-notifications.js
 */

const PB_URL = process.env.PB_URL || 'http://localhost:8090';

async function createTestCallbackRequest() {
  const testData = {
    name: 'Тестовый Пользователь',
    phone: '+7 (999) 123-45-67',
    message: 'Это тестовая заявка для проверки уведомлений',
    ip_address: '127.0.0.1'
  };

  try {
    console.log('🧪 Создаем тестовую заявку...');
    console.log('📋 Данные:', JSON.stringify(testData, null, 2));
    
    const response = await fetch(`${PB_URL}/api/collections/callback_requests/records`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Тестовая заявка создана успешно!');
      console.log('🆔 ID записи:', result.id);
      console.log('⏰ Время создания:', result.created);
      console.log('');
      console.log('📧 Проверьте email и Telegram на наличие уведомлений');
      console.log('📋 Ссылка на запись в админке:');
      console.log(`   ${PB_URL}/_/#/collections/callback_requests/records/${result.id}`);
    } else {
      const error = await response.text();
      console.error('❌ Ошибка создания заявки:', response.status, error);
    }
  } catch (error) {
    console.error('❌ Ошибка подключения:', error.message);
    console.log('');
    console.log('💡 Убедитесь, что:');
    console.log('   - PocketBase запущен на', PB_URL);
    console.log('   - Коллекция callback_requests существует');
    console.log('   - Настроены правила доступа для создания записей');
  }
}

async function checkPocketBaseStatus() {
  try {
    console.log('🔍 Проверяем доступность PocketBase...');
    const response = await fetch(`${PB_URL}/api/health`);
    
    if (response.ok) {
      console.log('✅ PocketBase доступен');
      return true;
    } else {
      console.log('❌ PocketBase недоступен:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Не удается подключиться к PocketBase:', error.message);
    return false;
  }
}

async function main() {
  console.log('🧪 Тестирование уведомлений о заявках на обратный звонок');
  console.log('=' .repeat(60));
  console.log('🌐 PocketBase URL:', PB_URL);
  console.log('');

  const isAvailable = await checkPocketBaseStatus();
  
  if (isAvailable) {
    await createTestCallbackRequest();
  } else {
    console.log('');
    console.log('💡 Для запуска PocketBase выполните:');
    console.log('   docker-compose up -d');
    console.log('   или');
    console.log('   ./pocketbase serve');
  }
}

// Проверяем, что скрипт запущен напрямую
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { createTestCallbackRequest, checkPocketBaseStatus };
