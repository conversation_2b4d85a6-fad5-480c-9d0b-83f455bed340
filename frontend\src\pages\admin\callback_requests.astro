---
import Layout from '@/layouts/Layout.astro';
import { CollectionManager } from '@/components/admin/CollectionManager';
import { isUserAuthenticated } from '@/middleware/auth';

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Если не авторизован, перенаправляем на главную админ-страницу
if (!isAuthenticated) {
  return Astro.redirect('/admin');
}
---

<Layout 
  title="Заявки на обратный звонок - Админ-панель"
  description="Управление заявками на обратный звонок от клиентов"
  noindex={true}
>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center space-x-4">
            <a 
              href="/admin" 
              class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              Назад к панели
            </a>
            <div class="h-4 w-px bg-gray-300"></div>
            <h1 class="text-xl font-semibold text-gray-900">Заявки на обратный звонок</h1>
          </div>
          
          <div class="flex items-center space-x-3">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-olive-100 text-olive-800">
              <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Активно
            </span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Info Banner -->
      <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
          <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
          <div>
            <h3 class="text-sm font-medium text-blue-800">Важная информация</h3>
            <p class="mt-1 text-sm text-blue-700">
              Здесь отображаются все заявки на обратный звонок от посетителей сайта. 
              Обязательно обрабатывайте новые заявки в течение 15 минут для максимальной конверсии.
            </p>
          </div>
        </div>
      </div>

      <!-- Collection Manager -->
      <CollectionManager
        collection="callback_requests"
        title="Заявки на обратный звонок"
        description="Управление заявками от клиентов на обратный звонок. Отмечайте обработанные заявки для контроля."
        pbUrl="https://pb.stom-line.ru"
        client:only="react"
      />
    </main>
  </div>
</Layout>

<style>
  /* Дополнительные стили для улучшения UX */
  .collection-manager {
    background-color: white;
    border-radius: 0.5rem; /* rounded-lg */
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); /* shadow-sm */
    border: 1px solid hsl(var(--border));
  }
  
  /* Стили для статусов заявок */
  .status-new {
    background-color: #fee2e2; /* red-100 */
    color: #991b1b; /* red-800 */
  }
  
  .status-processed {
    background-color: #e8f2e2; /* olive-100 */
    color: #2e531a; /* olive-800 */
  }
  
  /* Анимация для новых заявок */
  @keyframes pulse-red {
    0%, 100% {
      background-color: #fef2f2; /* red-50 */
    }
    50% {
      background-color: #fee2e2; /* red-100 */
    }
  }
  
  .new-request {
    animation: pulse-red 2s infinite;
  }
</style>

<script>
  // Автообновление страницы каждые 30 секунд для получения новых заявок
  if (typeof window !== 'undefined') {
    setInterval(() => {
      // Проверяем, есть ли новые заявки
      const currentTime = Date.now();
      const lastCheck = localStorage.getItem('lastCallbackCheck');
      
      if (!lastCheck || currentTime - parseInt(lastCheck) > 30000) {
        localStorage.setItem('lastCallbackCheck', currentTime.toString());
        // Можно добавить звуковое уведомление о новых заявках
      }
    }, 30000);
  }
</script>
