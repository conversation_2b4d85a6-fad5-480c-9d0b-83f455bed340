---
import Layout from '../../layouts/Layout.astro';
import PocketBase from 'pocketbase';
import { Button } from '../../components/ui/button';
import { CalendarIcon, ArrowLeftIcon } from 'lucide-react';
import EditButtonServer from '@/components/admin/EditButtonServer.astro';
import { generateArticleJsonLd } from '../../lib/seo-config';
import RDFaMarkup from '../../components/RDFaMarkup.astro';

// URL API
const PUBLIC_API_URL = 'https://pb.stom-line.ru';

export async function getStaticPaths() {
  const pb = new PocketBase(PUBLIC_API_URL);

  try {
    const news = await pb.collection('news').getFullList();

    return news.map((newsItem) => ({
      params: { slug: newsItem.slug || newsItem.id },
      props: { newsItem },
    }));
  } catch (error) {
    console.error('Ошибка при получении данных о новостях:', error);
    return [];
  }
}

const { newsItem } = Astro.props;

// Проверяем, что новость существует
if (!newsItem) {
  return Astro.redirect('/404');
}

// Функция для получения URL изображения
const getImageUrl = (newsItem: any) => {
  if (!newsItem.image) return '/placeholder-news.jpg';
  return `${PUBLIC_API_URL}/api/files/news/${newsItem.id}/${newsItem.image}`;
};

// Функция для форматирования даты
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// SEO данные
const title = newsItem?.meta_title || `${newsItem?.title} | Новости STOM-LINE`;
const description = newsItem?.meta_description || `${newsItem?.title} - новость стоматологической клиники STOM-LINE в Мурманске. Читайте последние события и обновления нашей клиники.`;
const image = newsItem.image ? getImageUrl(newsItem) : 'https://stom-line.ru/og-image.jpg';

// Генерируем структурированные данные для новости
const articleJsonLd = generateArticleJsonLd(newsItem, 'news');

// Формируем ключевые слова
const keywords = [
  'новости стоматологии',
  'STOM-LINE',
  'стоматология Мурманск',
  'новости клиники',
  newsItem?.title
].filter(Boolean);
---

<Layout
  title={title}
  description={description}
  keywords={keywords}
  image={image}
  type="article"
  jsonLd={articleJsonLd}
  article={{
    publishedTime: newsItem.date,
    modifiedTime: newsItem.updated,
    author: 'STOM-LINE',
    section: 'Новости'
  }}
>
  <div class="min-h-screen bg-gradient-to-b from-green-50 via-green-100 to-green-50">
    <!-- Background elements -->
    <div class="pointer-events-none absolute inset-0 z-0 overflow-hidden">
      <div class="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-[#8BC34A]/30 blur-[100px]"></div>
      <div class="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-[#85C026]/30 blur-[80px]"></div>
      <div class="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-[#8BC34A]/20 blur-[120px]"></div>
    </div>

    <!-- Grid lines -->
    <div class="pointer-events-none absolute inset-0 z-0  bg-center opacity-[0.05]"></div>

    <div class="relative z-10 container mx-auto px-4 py-16">
      <!-- Navigation -->
      <div class="mb-8">
        <Button variant="outline" asChild className="border-[#8BC34A]/30 text-[#4E8C29] hover:bg-[#8BC34A]/10">
          <a href="/news">
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Вернуться к новостям
          </a>
        </Button>
      </div>

      <!-- Article -->
      <article class="max-w-4xl mx-auto">
        <!-- Header -->
        <header class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-500 mb-4">
            <CalendarIcon className="h-4 w-4" />
            {formatDate(newsItem.date)}
          </div>

          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            {newsItem.title}
          </h1>

          <!-- Edit Button -->
          <div class="mb-6">
            <EditButtonServer collection="news" id={newsItem.id} />
          </div>
        </header>

        <!-- Featured Image -->
        {newsItem.image && (
          <div class="mb-8 rounded-2xl overflow-hidden shadow-2xl">
            <img
              src={getImageUrl(newsItem)}
              alt={newsItem.title}
              class="w-full h-64 md:h-96 object-cover"
            />
          </div>
        )}

        <!-- Content -->
        <div class="prose prose-lg max-w-none">
          <div 
            class="text-gray-700 leading-relaxed"
            set:html={newsItem.content}
          />
        </div>

        <!-- Share Section -->
        <div class="mt-12 pt-8 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <span class="text-sm font-medium text-gray-500">Поделиться:</span>
              <div class="flex gap-2">
                <button
                  class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-[#8BC34A]/30 bg-background text-[#4E8C29] hover:bg-[#8BC34A]/10 h-9 px-3"
                  onclick={`navigator.share ? navigator.share({title: '${newsItem.title}', url: window.location.href}) : navigator.clipboard.writeText(window.location.href)`}
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                  </svg>
                </button>
              </div>
            </div>

            <div class="text-sm text-gray-500">
              Опубликовано: {formatDate(newsItem.created)}
            </div>
          </div>
        </div>

        <!-- Related News -->
        <div class="mt-16">
          <h3 class="text-2xl font-bold text-gray-900 mb-8">Другие новости</h3>
          <div class="grid md:grid-cols-2 gap-6">
            <!-- Здесь можно добавить связанные новости -->
          </div>
        </div>
      </article>

      <!-- Call to Action -->
      <div class="mt-16 text-center">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-[#8BC34A]/20">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">
            Хотите быть в курсе всех новостей?
          </h3>
          <p class="text-gray-600 mb-6">
            Следите за нашими обновлениями и не пропускайте важные события клиники
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild className="bg-[#8BC34A] hover:bg-[#4E8C29] text-white">
              <a href="/news">Все новости</a>
            </Button>
            <Button asChild variant="outline" className="border-[#8BC34A]/30 text-[#4E8C29] hover:bg-[#8BC34A]/10">
              <a href="/contact">Связаться с нами</a>
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- RDFa разметка для новости -->
  <RDFaMarkup type="article" data={{...newsItem, type: 'news'}} />
</Layout>


