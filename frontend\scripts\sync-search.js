#!/usr/bin/env node

/**
 * Скрипт для синхронизации данных между PocketBase и MeiliSearch
 * 
 * Использование:
 * node scripts/sync-search.js [init|sync]
 * 
 * Параметры:
 * - init: полная инициализация (настройка индексов, синхронизация данных, создание общего индекса)
 * - sync: только синхронизация данных (по умолчанию)
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Загружаем переменные окружения из .env файла
try {
  const envPath = path.resolve(__dirname, '../.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    
    for (const line of envVars) {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    }
  }
} catch (error) {
  console.error('Ошибка при загрузке .env файла:', error);
}

// Получаем параметры из командной строки
const args = process.argv.slice(2);
const action = args[0] || 'sync';

// Определяем URL для запроса
const baseUrl = process.env.BASE_URL || 'http://localhost:4321';
const apiUrl = `${baseUrl}/api/sync-search`;
const apiKey = process.env.API_KEY || 'stomline-sync-key';

// Функция для выполнения HTTP запроса
function makeRequest(url, method, data, headers) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    const req = client.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({ statusCode: res.statusCode, data: parsedData });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Проверяем, запущен ли сервер
async function checkServerRunning() {
  try {
    await makeRequest(`${baseUrl}/api/health`, 'GET');
    return true;
  } catch (error) {
    return false;
  }
}

// Запускаем сервер, если он не запущен
async function startServerIfNeeded() {
  const isRunning = await checkServerRunning();
  
  if (!isRunning) {
    console.log('Сервер не запущен. Запускаем временный сервер...');
    
    // Запускаем сервер в фоновом режиме
    const serverProcess = execSync('cd .. && npm run dev', { 
      stdio: 'inherit',
      detached: true
    });
    
    // Ждем запуска сервера
    console.log('Ожидаем запуска сервера...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    return true;
  }
  
  return false;
}

// Основная функция
async function main() {
  try {
    console.log(`Запуск синхронизации данных с MeiliSearch (действие: ${action})...`);
    
    // Запускаем сервер, если нужно
    const serverStarted = await startServerIfNeeded();
    
    // Выполняем запрос к API
    const response = await makeRequest(apiUrl, 'POST', { action }, {
      'x-api-key': apiKey
    });
    
    if (response.statusCode === 200 && response.data.success) {
      console.log('Синхронизация успешно завершена!');
      console.log(response.data.message);
    } else {
      console.error('Ошибка при синхронизации данных:');
      console.error(response.data);
    }
    
    // Если мы запустили сервер, останавливаем его
    if (serverStarted) {
      console.log('Останавливаем временный сервер...');
      process.exit(0);
    }
  } catch (error) {
    console.error('Ошибка при выполнении скрипта:', error);
    process.exit(1);
  }
}

// Запускаем скрипт
main();
