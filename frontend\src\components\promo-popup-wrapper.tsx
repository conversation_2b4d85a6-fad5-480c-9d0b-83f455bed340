'use client'

import { useEffect, useState } from 'react'
import { PromoPopup } from './promo-popup'
import { usePromoPopup } from '@/hooks/use-promo-popup'
import { getPopupPromo, type Promo } from '@/lib/api'

export function PromoPopupWrapper() {
  const [promo, setPromo] = useState<Promo | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Загружаем акцию для поп-апа
  useEffect(() => {
    async function loadPromo() {
      try {
        const popupPromo = await getPopupPromo()

        // Если акция не найдена, создаем тестовую для демонстрации
        if (!popupPromo) {
          console.log('Акция для поп-апа не найдена, используем тестовую')
          const testPromo: Promo = {
            id: 'test-popup-promo',
            title: 'Только сегодня!',
            subtitle: 'Запишитесь на бесплатный осмотр — получите скидку 10% на первое лечение.',
            slug: 'popup-promo',
            content: `
              <div class="text-center">
                <h2 class="text-2xl font-bold text-olive-800 mb-4">Только сегодня!</h2>
                <p class="text-lg text-olive-700 mb-6">
                  Запишитесь на бесплатный осмотр — получите скидку 10% на первое лечение.
                </p>
                <div class="bg-olive-50 p-4 rounded-lg mb-6">
                  <p class="text-olive-800 font-semibold">
                    🎁 Что входит в бесплатный осмотр:
                  </p>
                  <ul class="text-olive-700 mt-2 text-left">
                    <li>• Консультация врача-стоматолога</li>
                    <li>• Составление плана лечения</li>
                    <li>• Рекомендации по уходу за полостью рта</li>
                  </ul>
                </div>
                <p class="text-sm text-olive-600">
                  ⏰ Предложение действует только сегодня!
                </p>
              </div>
            `,
            is_active: true,
            is_featured: false,
            sort_order: 999
          }
          setPromo(testPromo)
        } else {
          setPromo(popupPromo)
        }
      } catch (error) {
        console.error('Ошибка при загрузке акции для поп-апа:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadPromo()
  }, [])

  // Используем хук для управления поп-апом
  const { isVisible, closePopup } = usePromoPopup({
    promo,
    scrollThreshold: 50, // Показать при прокрутке 50%
    showOnExit: true, // Показать при попытке покинуть сайт
    showOnScroll: true, // Показать при прокрутке
    cooldownHours: 24 // Не показывать повторно 24 часа
  })

  // Не рендерим ничего во время загрузки или если нет акции
  if (isLoading || !promo) {
    return null
  }

  return (
    <PromoPopup
      promo={promo}
      isVisible={isVisible}
      onClose={closePopup}
    />
  )
}
