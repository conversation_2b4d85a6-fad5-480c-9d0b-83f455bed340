version: '3.8'

services:
  backend:
    build: .
    ports:
      - "8090:8090"
    volumes:
      # Только данные монтируются, хуки уже в образе
      - pocketbase_data:/pb/pb_data
    environment:
      - PB_ENCRYPTION_KEY=${PB_ENCRYPTION_KEY}
      # Email уведомления
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - NOTIFICATION_EMAIL_FROM=${NOTIFICATION_EMAIL_FROM}
      - NOTIFICATION_EMAIL_TO=${NOTIFICATION_EMAIL_TO}
      # Telegram уведомления
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      # Общие настройки
      - SITE_URL=${SITE_URL}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8090/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

volumes:
  pocketbase_data:
    driver: local
