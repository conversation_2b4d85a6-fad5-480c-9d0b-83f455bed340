'use client'

import { useRef, useState } from 'react'
import { motion, useInView } from 'framer-motion'
import { Calendar, ArrowRight, Clock, Tag as TagIcon, Filter, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { EditButton } from '@/components/admin/EditButton'
import { type Promo } from '@/lib/api'

interface PromosPageProps {
  promos: Promo[]
}

function PromoCard({ promo, index }: { promo: Promo; index: number }) {
  const cardRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(cardRef, { once: true, margin: '-50px 0px' })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  }

  const isActive = promo.is_active !== false
  const isExpired = promo.end_date && new Date(promo.end_date) < new Date()

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={`group relative overflow-hidden rounded-xl bg-white shadow-md transition-all hover:shadow-lg ${
        !isActive || isExpired ? 'opacity-75' : ''
      }`}
    >
      <EditButton
        collection="promos"
        id={promo.id}
        position="top-right"
        variant="text"
        size="sm"
        className="bg-white/90 border border-gray-200 shadow-sm"
      />

      <div className="relative h-48 overflow-hidden">
        {promo.image ? (
          <img
            src={`${import.meta.env.PUBLIC_API_URL || 'https://pb.stom-line.ru'}/api/files/promos/${promo.id}/${promo.image}`}
            alt={promo.title}
            className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-olive-100 text-olive-600">
            <span className="text-xl font-bold">АКЦИЯ</span>
          </div>
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

        {/* Статус акции */}
        <div className="absolute top-4 left-4">
          {promo.is_featured && (
            <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white mb-2">
              Рекомендуем
            </Badge>
          )}
          {!isActive && (
            <Badge variant="secondary" className="bg-gray-500 text-white">
              Неактивна
            </Badge>
          )}
          {isExpired && (
            <Badge variant="destructive" className="bg-red-500 text-white">
              Истекла
            </Badge>
          )}
        </div>

        {/* Даты акции */}
        {(promo.start_date || promo.end_date) && (
          <div className="absolute bottom-4 left-4 flex items-center rounded-full bg-white/90 px-3 py-1 text-sm backdrop-blur-sm">
            <Calendar className="mr-1 h-4 w-4 text-olive-600" />
            <span>
              {promo.start_date && formatDate(promo.start_date)}
              {promo.start_date && promo.end_date && ' - '}
              {promo.end_date && formatDate(promo.end_date)}
            </span>
          </div>
        )}
      </div>

      <div className="p-6">
        <h3 className="mb-2 text-xl font-bold text-olive-800">{promo.title}</h3>
        {promo.subtitle && (
          <p className="mb-4 text-olive-600 font-medium">{promo.subtitle}</p>
        )}

        {promo.content && (
          <div
            className="prose prose-olive line-clamp-3 max-w-none text-sm text-gray-700"
            dangerouslySetInnerHTML={{ __html: promo.content }}
          />
        )}

        <div className="mt-4 flex items-center justify-between">
          <Button
            variant="link"
            className="text-olive-600 h-auto p-0 font-medium hover:text-olive-700"
            asChild
          >
            <a href={`/promos/${promo.slug || promo.id}`}>
              Подробнее <ArrowRight className="ml-1 h-4 w-4" />
            </a>
          </Button>

          {promo.end_date && (
            <div className="flex items-center text-xs text-gray-500">
              <Clock className="mr-1 h-3 w-3" />
              <span>до {formatDate(promo.end_date)}</span>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

export function PromosPage({ promos }: PromosPageProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: true, margin: '-100px 0px' })

  const [searchTerm, setSearchTerm] = useState('')
  const [showInactive, setShowInactive] = useState(true) // По умолчанию показываем все акции

  // Отладка: выводим данные акций в консоль
  console.log('PromosPage получил акции:', promos.length, promos.map(p => ({
    id: p.id,
    title: p.title,
    is_active: p.is_active,
    end_date: p.end_date
  })))

  // Фильтрация акций
  const filteredPromos = promos.filter(promo => {
    const matchesSearch = promo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (promo.subtitle && promo.subtitle.toLowerCase().includes(searchTerm.toLowerCase()))

    // Проверяем активность акции более мягко
    const isActive = promo.is_active !== false && (!promo.end_date || new Date(promo.end_date) >= new Date())

    console.log(`Акция "${promo.title}": is_active=${promo.is_active}, isActive=${isActive}, showInactive=${showInactive}`)

    return matchesSearch && (showInactive || isActive)
  })

  // Сортировка: сначала активные и избранные, потом по дате
  const sortedPromos = filteredPromos.sort((a, b) => {
    if (a.is_featured && !b.is_featured) return -1
    if (!a.is_featured && b.is_featured) return 1
    if (a.is_active && !b.is_active) return -1
    if (!a.is_active && b.is_active) return 1

    const dateA = a.start_date ? new Date(a.start_date) : new Date(0)
    const dateB = b.start_date ? new Date(b.start_date) : new Date(0)
    return dateB.getTime() - dateA.getTime()
  })

  return (
    <div ref={containerRef} className="relative min-h-screen bg-gradient-to-br from-olive-50 via-white to-olive-50/50">
      {/* Enhanced Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <motion.div
          animate={{
            x: [0, 30, 0],
            y: [0, -20, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -left-[10%] top-[20%] h-[400px] w-[400px] rounded-full bg-gradient-to-r from-olive-300/20 to-olive-400/30 blur-[120px]"
        />
        <motion.div
          animate={{
            x: [0, -25, 0],
            y: [0, 15, 0],
            scale: [1, 0.9, 1]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
          className="absolute -right-[5%] top-[40%] h-[300px] w-[300px] rounded-full bg-gradient-to-l from-olive-400/25 to-olive-500/20 blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, 20, 0],
            y: [0, -30, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 10
          }}
          className="absolute bottom-[20%] left-[20%] h-[350px] w-[350px] rounded-full bg-gradient-to-tr from-olive-300/15 to-olive-200/25 blur-[140px]"
        />
      </div>

      {/* Grid lines */}
      <div className="pointer-events-none absolute inset-0 z-0  bg-center opacity-[0.05]" />

      {/* Header Section */}
      <motion.div
        className="relative py-16 md:py-24 z-10"
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mx-auto max-w-4xl text-center"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Badge variant="flat" >
                <TagIcon className="h-4 w-4 mr-2" />
                Специальные предложения
              </Badge>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gray-900 via-olive-800 to-gray-900 bg-clip-text text-transparent mb-8"
            >
              Акции и специальные предложения
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto mb-12 leading-relaxed"
            >
              Воспользуйтесь нашими специальными предложениями и получите качественное лечение по выгодной цене
            </motion.p>
          </motion.div>
        </div>
      </motion.div>

      <div className="relative container mx-auto px-4 md:px-6 pb-16 z-10">
        {sortedPromos.length > 0 ? (
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {sortedPromos.map((promo, index) => (
              <PromoCard key={promo.id} promo={promo} index={index} />
            ))}
          </div>
        ) : (
          <motion.div
            className="text-center py-16"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="mx-auto max-w-md">
              <TagIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm ? 'Акции не найдены' : 'Нет доступных акций'}
              </h3>
              <p className="text-gray-500">
                {searchTerm
                  ? 'Попробуйте изменить поисковый запрос или показать все акции'
                  : 'В данный момент нет активных акций. Следите за обновлениями!'
                }
              </p>
              {searchTerm && (
                <Button
                  variant="outline"
                  onClick={() => setSearchTerm('')}
                  className="mt-4 border-olive-600 text-olive-600 hover:bg-olive-50"
                >
                  Очистить поиск
                </Button>
              )}
            </div>
          </motion.div>
        )}

        {/* Call to Action */}
        {sortedPromos.length > 0 && (
          <motion.div
            className="mt-16 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="bg-white rounded-xl shadow-md p-8 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-olive-800 mb-4">
                Не нашли подходящую акцию?
              </h3>
              <p className="text-olive-700 mb-6">
                Свяжитесь с нами, и мы подберем для вас оптимальное предложение
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-xl">
                  <a href="tel:+78152525708" className="flex items-center">
                    Позвонить сейчас
                  </a>
                </Button>
                <Button asChild variant="outline" className="border-olive-200 text-olive-700 hover:bg-olive-50 shadow-lg">
                  <a href="/services">Все услуги</a>
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}
