import * as React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Save, X, ExternalLink } from 'lucide-react';
import { getRecord, updateRecord } from '@/lib/pocketbase-admin';

interface QuickEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  collection: string;
  recordId: string;
  pbUrl: string;
}

interface FieldConfig {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'number' | 'email' | 'url' | 'date' | 'checkbox';
  required?: boolean;
}

const getFieldsConfig = (collection: string): FieldConfig[] => {
  const configs: Record<string, FieldConfig[]> = {
    doctors: [
      { name: 'name', label: 'Имя', type: 'text', required: true },
      { name: 'position', label: 'Должность', type: 'text', required: true },
      { name: 'specialization', label: 'Специализация', type: 'text' },
      { name: 'experience', label: 'Опыт работы', type: 'number' },
      { name: 'education', label: 'Образование', type: 'textarea' },
      { name: 'short_description', label: 'Краткое описание', type: 'textarea' },
    ],
    services: [
      { name: 'name', label: 'Название', type: 'text', required: true },
      { name: 'short_description', label: 'Краткое описание', type: 'textarea' },
      { name: 'price', label: 'Цена', type: 'number' },
      { name: 'duration', label: 'Длительность (мин)', type: 'number' },
    ],
    reviews: [
      { name: 'name', label: 'Имя пациента', type: 'text', required: true },
      { name: 'rating', label: 'Рейтинг (1-5)', type: 'number', required: true },
      { name: 'text', label: 'Текст отзыва', type: 'textarea', required: true },
      { name: 'date', label: 'Дата', type: 'date' },
    ],
    faq: [
      { name: 'question', label: 'Вопрос', type: 'text', required: true },
      { name: 'answer', label: 'Ответ', type: 'textarea', required: true },
      { name: 'category', label: 'Категория', type: 'text' },
    ],
    news: [
      { name: 'title', label: 'Заголовок', type: 'text', required: true },
      { name: 'slug', label: 'URL-адрес', type: 'text', required: true },
      { name: 'date', label: 'Дата публикации', type: 'date', required: true },
      { name: 'excerpt', label: 'Краткое описание', type: 'textarea' },
    ],
    promos: [
      { name: 'title', label: 'Название акции', type: 'text', required: true },
      { name: 'description', label: 'Описание', type: 'textarea', required: true },
      { name: 'discount', label: 'Размер скидки (%)', type: 'number' },
      { name: 'valid_until', label: 'Действует до', type: 'date' },
    ],
    html_blocks: [
      { name: 'content', label: 'HTML-контент', type: 'textarea', required: true },
    ],
    personal: [
      { name: 'surname', label: 'Фамилия', type: 'text' },
      { name: 'name', label: 'Имя', type: 'text' },
      { name: 'patronymic', label: 'Отчество', type: 'text' },
      { name: 'position', label: 'Должность', type: 'text' },
      { name: 'about', label: 'О сотруднике', type: 'textarea' },
    ],
  };

  return configs[collection] || [
    { name: 'name', label: 'Название', type: 'text', required: true },
    { name: 'title', label: 'Заголовок', type: 'text' },
  ];
};

export const QuickEditModal: React.FC<QuickEditModalProps> = ({
  isOpen,
  onClose,
  collection,
  recordId,
  pbUrl
}) => {
  const [record, setRecord] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(false);
  const [saving, setSaving] = React.useState(false);
  const [error, setError] = React.useState('');
  const [formData, setFormData] = React.useState<Record<string, any>>({});

  const fieldsConfig = getFieldsConfig(collection);

  React.useEffect(() => {
    if (isOpen && recordId) {
      loadRecord();
    }
  }, [isOpen, recordId]);

  const loadRecord = async () => {
    setLoading(true);
    setError('');
    try {
      const data = await getRecord(collection, recordId);
      setRecord(data);
      setFormData(data);
    } catch (err: any) {
      console.error('Error loading record:', err);
      setError(err?.message || 'Ошибка загрузки записи');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    setError('');
    try {
      await updateRecord(collection, recordId, formData);
      onClose();
    } catch (err: any) {
      console.error('Error saving record:', err);
      setError(err?.message || 'Ошибка сохранения');
    } finally {
      setSaving(false);
    }
  };

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const renderField = (field: FieldConfig) => {
    const value = formData[field.name] || '';

    switch (field.type) {
      case 'textarea':
        return (
          <Textarea
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={`Введите ${field.label.toLowerCase()}`}
            rows={3}
          />
        );
      case 'number':
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleFieldChange(field.name, Number(e.target.value))}
            placeholder={`Введите ${field.label.toLowerCase()}`}
          />
        );
      case 'date':
        return (
          <Input
            type="date"
            value={value ? new Date(value).toISOString().split('T')[0] : ''}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
          />
        );
      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={Boolean(value)}
              onChange={(e) => handleFieldChange(field.name, e.target.checked)}
              className="rounded border-gray-300"
            />
            <span className="text-sm text-gray-600">Да</span>
          </div>
        );
      default:
        return (
          <Input
            type={field.type}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={`Введите ${field.label.toLowerCase()}`}
          />
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto w-[90vw]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Быстрое редактирование</span>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">{collection}</Badge>
              <Button
                size="sm"
                variant="outline"
                onClick={() => window.open(`${pbUrl}/_/#/collections?collection=pbc_${collection}&recordId=${recordId}`, '_blank')}
              >
                <ExternalLink className="w-4 h-4" />
              </Button>
            </div>
          </DialogTitle>
          <DialogDescription>
            Редактирование основных полей записи. Для полного редактирования используйте PocketBase Admin.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="w-8 h-8 border-2 border-[#8BC34A]/30 border-t-[#8BC34A] rounded-full animate-spin" />
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={loadRecord} variant="outline">
              Попробовать снова
            </Button>
          </div>
        ) : record ? (
          <div className="space-y-4">
            {fieldsConfig.map((field) => (
              <div key={field.name} className="space-y-2">
                <Label htmlFor={field.name} className="flex items-center gap-2">
                  {field.label}
                  {field.required && <span className="text-red-500">*</span>}
                </Label>
                {renderField(field)}
              </div>
            ))}

            <div className="flex items-center justify-between pt-4 border-t">
              <div className="text-xs text-gray-500">
                ID: {recordId}
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={onClose}>
                  <X className="w-4 h-4 mr-1" />
                  Отмена
                </Button>
                <Button 
                  onClick={handleSave} 
                  disabled={saving}
                  className="bg-[#8BC34A] hover:bg-[#4E8C29]"
                >
                  {saving ? (
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-1" />
                  ) : (
                    <Save className="w-4 h-4 mr-1" />
                  )}
                  {saving ? 'Сохранение...' : 'Сохранить'}
                </Button>
              </div>
            </div>
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  );
};
