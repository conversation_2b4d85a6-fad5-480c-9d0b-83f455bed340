---
import { isUserAuthenticated } from '@/middleware/auth';

interface Props {
  collection: string;
  id: string;
  className?: string;
  text?: string;
}

const { collection, id, className = '', text = 'Редактировать' } = Astro.props;

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);
---

{isAuthenticated && (
  <div class={`inline-block ${className}`}>
    <a 
      href={`/admin/edit/${collection}/${id}`} 
      target="_blank" 
      rel="noopener noreferrer" 
      class="inline-flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 hover:text-blue-700 transition-colors duration-200"
    >
      <svg 
        class="w-3 h-3" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
        />
      </svg>
      {text}
    </a>
  </div>
)}
