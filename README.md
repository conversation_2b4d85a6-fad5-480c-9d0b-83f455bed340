# Stom-Line

Сайт стоматологической клиники Stom-Line с поддержкой поиска на базе MeiliSearch.

## Структура проекта

- `frontend/` - Frontend на базе Astro с React компонентами
- `backend/` - Backend на базе PocketBase с хуками для синхронизации данных
- `docker-compose.yml` - Конфигурация Docker Compose для запуска всего проекта

## Технологии

- **Frontend**: Astro, React, Tailwind CSS
- **Backend**: PocketBase (SQLite)
- **Поиск**: MeiliSearch
- **Контейнеризация**: Docker, Docker Compose

## Запуск проекта

### С использованием Docker Compose

1. Убедитесь, что у вас установлены Docker и Docker Compose
2. Запустите проект с помощью скрипта:

```bash
./start.sh
```

Или вручную:

```bash
# Запуск основных сервисов
docker-compose up -d pocketbase meilisearch frontend

# Подождите, пока сервисы запустятся (примерно 30 секунд)

# Запуск инициализации MeiliSearch
docker-compose up init-meilisearch
```

### Без Docker

#### Frontend (Astro)

```bash
cd frontend
npm install
npm run dev
```

#### Backend (PocketBase)

1. Скачайте PocketBase с [официального сайта](https://pocketbase.io/docs/)
2. Распакуйте архив
3. Скопируйте файлы из `backend/hooks` в папку `pb_hooks`
4. Запустите PocketBase:

```bash
./pocketbase serve
```

#### MeiliSearch

1. Установите MeiliSearch согласно [официальной документации](https://docs.meilisearch.com/learn/getting_started/installation.html)
2. Запустите MeiliSearch:

```bash
meilisearch --master-key Nc040stomline
```

3. Инициализируйте индексы и синхронизируйте данные:

```bash
cd backend
node init-meilisearch.js
```

## Доступ к сервисам

- **Frontend**: http://localhost:4321
- **PocketBase Admin**: http://localhost:8090/_/
- **MeiliSearch Admin**: http://localhost:7700

## Поиск по сайту

Поиск доступен через кнопку в верхнем меню сайта. Он позволяет искать по следующим разделам:

- Специалисты
- Услуги
- Акции
- FAQ
- Новости
- Страницы

## Автоматическая синхронизация данных

Проект настроен на автоматическую синхронизацию данных между PocketBase и MeiliSearch. При изменении данных в PocketBase (создание, обновление, удаление) они автоматически синхронизируются с MeiliSearch через хуки.

## Переменные окружения

### Frontend (.env)

```
PUBLIC_API_URL=http://localhost:8090
PUBLIC_SEARCH_URL=http://localhost:7700
PUBLIC_SEARCH_API_KEY=Nc040stomline
API_KEY=stomline-sync-key
```

### Backend (переменные окружения Docker)

```
FRONTEND_URL=http://frontend:4321
API_KEY=stomline-sync-key
```

### MeiliSearch (переменные окружения Docker)

```
MEILI_MASTER_KEY=Nc040stomline
MEILI_NO_ANALYTICS=true
MEILI_ENV=production
```
