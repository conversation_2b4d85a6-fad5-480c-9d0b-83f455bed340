/**
 * PocketBase хук для синхронизации данных с MeiliSearch
 * 
 * Этот скрипт автоматически запускается при изменении данных в PocketBase
 * и синхронизирует их с MeiliSearch.
 * 
 * Для работы скрипта необходимо:
 * 1. Поместить его в папку pb_hooks
 * 2. Настроить переменные окружения в PocketBase
 */

// Коллекции, которые нужно синхронизировать с MeiliSearch
const COLLECTIONS_TO_SYNC = [
  'doctors',
  'services',
  'faq',
  'news',
  'promos',
  'prices',
  'pages',
  'service_categories',
  'html_blocks',
  'personal',
];

// Таймаут для дебаунсинга (мс)
const DEBOUNCE_TIMEOUT = 5000;

// Переменная для хранения таймера дебаунсинга
let debounceTimer = null;

// Переменная для отслеживания последней синхронизации
let lastSyncTime = 0;

// Минимальный интервал между синхронизациями (мс)
const MIN_SYNC_INTERVAL = 60000; // 1 минута

// Функция для выполнения HTTP запроса
function makeRequest(url, method, data, headers) {
  const http = require('http');
  const https = require('https');
  
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    const req = client.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({ statusCode: res.statusCode, data: parsedData });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Функция для синхронизации данных с MeiliSearch
async function syncWithMeiliSearch() {
  try {
    // Проверяем, не слишком ли часто запускаем синхронизацию
    const now = Date.now();
    if (now - lastSyncTime < MIN_SYNC_INTERVAL) {
      console.log(`[MeiliSearch] Синхронизация пропущена: слишком частые запросы (последняя была ${Math.round((now - lastSyncTime) / 1000)} сек. назад)`);
      return;
    }
    
    // Получаем переменные окружения
    const apiUrl = $os.getenv('FRONTEND_URL') || 'http://frontend:4321';
    const syncEndpoint = `${apiUrl}/api/sync-search`;
    const apiKey = $os.getenv('API_KEY') || 'stomline-sync-key';
    
    console.log(`[MeiliSearch] Запуск синхронизации с MeiliSearch через ${syncEndpoint}...`);
    
    // Выполняем запрос к API
    const response = await makeRequest(syncEndpoint, 'POST', { action: 'sync' }, {
      'x-api-key': apiKey
    });
    
    if (response.statusCode === 200 && response.data.success) {
      console.log('[MeiliSearch] Синхронизация успешно завершена!');
      lastSyncTime = Date.now();
    } else {
      console.error('[MeiliSearch] Ошибка при синхронизации данных:');
      console.error(response.data);
    }
  } catch (error) {
    console.error('[MeiliSearch] Ошибка при выполнении синхронизации:', error);
  }
}

// Функция для дебаунсинга синхронизации
function debouncedSync() {
  // Очищаем предыдущий таймер, если он был
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  
  // Устанавливаем новый таймер
  debounceTimer = setTimeout(() => {
    syncWithMeiliSearch();
    debounceTimer = null;
  }, DEBOUNCE_TIMEOUT);
}

// Регистрируем обработчики событий для всех коллекций
COLLECTIONS_TO_SYNC.forEach(collection => {
  // Обработчик создания записи
  onRecordAfterCreateRequest(collection, (e) => {
    console.log(`[MeiliSearch] Создана запись в коллекции ${collection}`);
    debouncedSync();
  });
  
  // Обработчик обновления записи
  onRecordAfterUpdateRequest(collection, (e) => {
    console.log(`[MeiliSearch] Обновлена запись в коллекции ${collection}`);
    debouncedSync();
  });
  
  // Обработчик удаления записи
  onRecordAfterDeleteRequest(collection, (e) => {
    console.log(`[MeiliSearch] Удалена запись из коллекции ${collection}`);
    debouncedSync();
  });
});

// Выполняем начальную синхронизацию при запуске PocketBase
onBootstrapRequest(() => {
  console.log('[MeiliSearch] PocketBase запущен, выполняем начальную синхронизацию...');
  
  // Даем время на инициализацию всех сервисов
  setTimeout(() => {
    syncWithMeiliSearch();
  }, 30000); // 30 секунд задержки
});
