#!/usr/bin/env node

/**
 * Скрипт для проверки индексов MeiliSearch
 */

const { MeiliSearch } = require('meilisearch');

async function main() {
  try {
    // Получаем URL MeiliSearch из переменных окружения или используем значение по умолчанию
    const searchUrl = process.env.PUBLIC_SEARCH_URL || 'https://search.stom-line.ru';
    const searchApiKey = process.env.PUBLIC_SEARCH_API_KEY || 'Nc040stomline';
    
    console.log(`Подключение к MeiliSearch: ${searchUrl}`);
    
    // Создаем экземпляр MeiliSearch
    const searchClient = new MeiliSearch({
      host: searchUrl,
      apiKey: searchApiKey
    });
    
    // Получаем список всех индексов
    console.log('\n=== Список индексов ===');
    const indexes = await searchClient.getIndexes();
    console.log(`Всего индексов: ${indexes.results.length}`);
    
    for (const index of indexes.results) {
      console.log(`- ${index.uid}: ${index.primaryKey || 'нет первичного ключа'}`);
    }
    
    // Проверяем каждый индекс
    const indexesToCheck = ['doctors', 'services', 'prices', 'all'];
    
    for (const indexName of indexesToCheck) {
      console.log(`\n=== Проверка индекса "${indexName}" ===`);
      
      try {
        const index = searchClient.index(indexName);
        const stats = await index.getStats();
        console.log(`Количество документов: ${stats.numberOfDocuments}`);
        
        if (stats.numberOfDocuments > 0) {
          // Получаем первые 3 документа
          const documents = await index.getDocuments({ limit: 3 });
          console.log(`Примеры документов (${Math.min(3, documents.results.length)} из ${documents.results.length}):`);
          
          for (const doc of documents.results.slice(0, 3)) {
            console.log(JSON.stringify(doc, null, 2));
          }
        }
      } catch (error) {
        console.error(`Ошибка при проверке индекса "${indexName}":`, error.message);
      }
    }
    
    // Проверяем настройки индексов
    for (const indexName of indexesToCheck) {
      console.log(`\n=== Настройки индекса "${indexName}" ===`);
      
      try {
        const index = searchClient.index(indexName);
        const settings = await index.getSettings();
        console.log('Поисковые атрибуты:', settings.searchableAttributes);
        console.log('Отображаемые атрибуты:', settings.displayedAttributes);
        console.log('Фильтруемые атрибуты:', settings.filterableAttributes);
        console.log('Сортируемые атрибуты:', settings.sortableAttributes);
      } catch (error) {
        console.error(`Ошибка при получении настроек индекса "${indexName}":`, error.message);
      }
    }
    
    console.log('\nПроверка завершена.');
  } catch (error) {
    console.error('Ошибка при выполнении скрипта:', error);
    process.exit(1);
  }
}

// Запускаем скрипт
main();
