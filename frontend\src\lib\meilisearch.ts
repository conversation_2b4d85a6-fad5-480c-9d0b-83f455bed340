import { MeiliSearch } from 'meilisearch';

// Создаем экземпляр MeiliSearch с URL и API ключом из переменных окружения
const searchUrl = import.meta.env.PUBLIC_SEARCH_URL || 'https://search.stom-line.ru';
const searchApiKey = import.meta.env.PUBLIC_SEARCH_API_KEY || 'Nc040stomline';

// Проверяем, что URL имеет правильный формат
let baseUrl = searchUrl;
if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
  baseUrl = `https://${baseUrl}`;
}

console.log('Используемый URL для MeiliSearch:', baseUrl);

// Создаем клиент MeiliSearch
const searchClient = new MeiliSearch({
  host: baseUrl,
  apiKey: searchApiKey
});

// Индексы для различных типов данных
const INDEXES = {
  DOCTORS: 'doctors',
  SERVICES: 'services',
  FAQ: 'faq',
  NEWS: 'news',
  PROMOS: 'promos',
  PRICES: 'prices',
  PAGES: 'pages'
};

/**
 * Поиск по всем индексам
 * @param query Поисковый запрос
 * @param options Опции поиска
 */
export async function searchAll(query: string, options: any = {}) {
  try {
    // Выполняем поиск по всем индексам параллельно
    const results = await Promise.all(
      Object.values(INDEXES).map(async (indexName) => {
        try {
          const index = searchClient.index(indexName);
          const result = await index.search(query, options);
          return {
            indexName,
            ...result
          };
        } catch (error) {
          console.error(`Ошибка при поиске в индексе ${indexName}:`, error);
          return {
            indexName,
            hits: [],
            estimatedTotalHits: 0,
            processingTimeMs: 0,
            query
          };
        }
      })
    );

    // Фильтруем результаты, оставляя только те индексы, где есть совпадения
    return results.filter(result => result.hits.length > 0);
  } catch (error) {
    console.error('Ошибка при выполнении поиска:', error);
    return [];
  }
}

/**
 * Поиск по конкретному индексу
 * @param indexName Название индекса
 * @param query Поисковый запрос
 * @param options Опции поиска
 */
export async function searchIndex(indexName: string, query: string, options: any = {}) {
  try {
    const index = searchClient.index(indexName);
    return await index.search(query, options);
  } catch (error) {
    console.error(`Ошибка при поиске в индексе ${indexName}:`, error);
    return {
      hits: [],
      estimatedTotalHits: 0,
      processingTimeMs: 0,
      query
    };
  }
}

// Экспортируем клиент и индексы для использования в других файлах
export { searchClient, INDEXES };

// Экспортируем типы для использования в компонентах
export type SearchResult = {
  indexName: string;
  hits: any[];
  estimatedTotalHits: number;
  processingTimeMs: number;
  query: string;
};
