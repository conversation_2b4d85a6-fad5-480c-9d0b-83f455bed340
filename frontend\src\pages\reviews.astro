---
import Layout from '@/layouts/Layout.astro'
import { ReviewsPage } from '@/components/reviews-page'
import { getReviews, type Review } from '@/lib/api'
import { getPageSEO } from '@/lib/seo-config'
import RDFaMarkup from '@/components/RDFaMarkup.astro'
import { isUserAuthenticated } from '@/middleware/auth'

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Получаем SEO данные для страницы отзывов
const seo = getPageSEO('reviews');

// Получаем данные из PocketBase
let reviews: Review[] = [];

try {
  const response = await getReviews();
  console.log('Получено отзывов из PocketBase на странице отзывов:', response?.length || 0);

  if (response && response.length > 0) {
    reviews = response;
  } else {
    console.warn('Нет данных об отзывах в PocketBase');
  }
} catch (error) {
  console.error('Ошибка при получении отзывов:', error);
}
---

<Layout
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}
  type={seo.type}
>
  <ReviewsPage reviews={reviews} isAuthenticated={isAuthenticated} client:load />

  <!-- RDFa разметка для отзывов -->
  <RDFaMarkup type="review" data={reviews} />
</Layout>
